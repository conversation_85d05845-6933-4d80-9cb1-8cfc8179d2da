package cloud.ipanda.jobplusv8.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 面试记录实体类
 *
 * 功能说明：
 * 1. 记录用户的面试会话信息
 * 2. 管理面试会话的生命周期和状态
 * 3. 记录面试的时长和内容
 * 4. 支持面试录音和转录文本的存储
 * 5. 记录面试公司和岗位信息
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("interview_records")
public class InterviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 面试记录ID（唯一标识）
     */
    @TableField("interview_id")
    private String interviewId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 面试公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 面试岗位
     */
    @TableField("position")
    private String position;

    /**
     * WebSocket会话ID
     */
    @TableField("websocket_session_id")
    private String websocketSessionId;

    /**
     * 面试状态：1-准备中，2-进行中，3-已结束，4-超时结束，5-强制结束
     */
    @TableField("status")
    private Integer status;

    /**
     * 面试开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 面试结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 面试持续时长（秒）
     */
    @TableField("duration")
    private Long duration;

    /**
     * 面试录音文件路径
     */
    @TableField("audio_file_path")
    private String audioFilePath;

    /**
     * 录音文件大小（字节）
     */
    @TableField("audio_file_size")
    private Long audioFileSize;

    /**
     * 面试转录文本
     */
    @TableField("transcript_text")
    private String transcriptText;

    /**
     * 面试总字数
     */
    @TableField("total_words")
    private Integer totalWords;

    /**
     * 面试评分（1-10分）
     */
    @TableField("interview_score")
    private Integer interviewScore;

    /**
     * 面试评价/反馈
     */
    @TableField("interview_feedback")
    private String interviewFeedback;

    /**
     * 客户端IP地址
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 结束原因：NORMAL-正常结束，TIMEOUT-超时，FORCE-强制结束，ERROR-错误结束
     */
    @TableField("end_reason")
    private String endReason;

    /**
     * 面试备注信息
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 面试状态枚举
     */
    public static class Status {
        public static final Integer PREPARING = 1;   // 准备中
        public static final Integer IN_PROGRESS = 2; // 进行中
        public static final Integer ENDED = 3;       // 已结束
        public static final Integer TIMEOUT = 4;     // 超时结束
        public static final Integer FORCED = 5;      // 强制结束
    }

    /**
     * 结束原因枚举
     */
    public static class EndReason {
        public static final String NORMAL = "NORMAL";     // 正常结束
        public static final String TIMEOUT = "TIMEOUT";   // 超时结束
        public static final String FORCE = "FORCE";       // 强制结束
        public static final String ERROR = "ERROR";       // 错误结束
        public static final String NO_DURATION = "NO_DURATION"; // 时长不足
    }
}
