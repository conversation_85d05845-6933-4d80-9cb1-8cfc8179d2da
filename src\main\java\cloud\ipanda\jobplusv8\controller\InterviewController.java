package cloud.ipanda.jobplusv8.controller;

import cloud.ipanda.jobplusv8.entity.InterviewRecord;
import cloud.ipanda.jobplusv8.service.InterviewRecordService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 面试管理控制器
 *
 * 功能说明：
 * 1. 创建面试记录
 * 2. 管理面试会话
 * 3. 查询面试历史
 * 4. 面试统计分析
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-08-05
 */
@RestController
@RequestMapping("/api/interview")
@Api(tags = "面试管理", description = "面试记录和会话管理相关API")
@Slf4j
public class InterviewController extends BaseController {

    @Autowired
    private InterviewRecordService interviewRecordService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 创建面试记录
     */
    @PostMapping("/create")
    @ApiOperation(
            value = "创建面试记录",
            notes = "用户输入面试公司和岗位，创建一个新的面试记录，返回面试记录ID和WebSocket会话ID"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "创建成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 401, message = "未授权"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> createInterview(
            @ApiParam(value = "面试公司名称", required = true, example = "阿里巴巴")
            @RequestParam String companyName,
            @ApiParam(value = "面试岗位", required = true, example = "Java开发工程师")
            @RequestParam String position,
            HttpServletRequest request) {

        try {
            // 从JWT中获取用户信息
            String token = jwtUtil.getTokenFromRequest(request);
            if (token == null) {
                return error(401, "未找到认证令牌");
            }

            Long userId = jwtUtil.getUserIdFromToken(token);
            String username = jwtUtil.getUsernameFromToken(token);

            if (userId == null || username == null) {
                return error(401, "认证令牌无效");
            }

            // 参数验证
            if (companyName == null || companyName.trim().isEmpty()) {
                return error(400, "面试公司名称不能为空");
            }

            if (position == null || position.trim().isEmpty()) {
                return error(400, "面试岗位不能为空");
            }

            // 创建面试记录
            InterviewRecord interviewRecord = interviewRecordService.createInterviewRecord(
                    userId, username, companyName.trim(), position.trim());

            if (interviewRecord != null) {
                Map<String, Object> data = new HashMap<>();
                data.put("interviewId", interviewRecord.getInterviewId());
                data.put("websocketSessionId", interviewRecord.getWebsocketSessionId());
                data.put("companyName", interviewRecord.getCompanyName());
                data.put("position", interviewRecord.getPosition());
                data.put("status", interviewRecord.getStatus());
                data.put("createTime", interviewRecord.getCreateTime());
                data.put("websocketUrl", "ws://localhost:80/ws/interview/meeting/" + interviewRecord.getWebsocketSessionId());

                log.info("【创建面试记录成功】用户: {}, 公司: {}, 岗位: {}, 面试ID: {}",
                        username, companyName, position, interviewRecord.getInterviewId());

                return success("面试记录创建成功", data);
            } else {
                return error(500, "创建面试记录失败");
            }

        } catch (Exception e) {
            log.error("【创建面试记录异常】公司: {}, 岗位: {}, 错误: {}", companyName, position, e.getMessage(), e);
            return error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 获取面试记录详情
     */
    @GetMapping("/{interviewId}")
    @ApiOperation(
            value = "获取面试记录详情",
            notes = "根据面试记录ID获取详细信息"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 401, message = "未授权"),
            @ApiResponse(code = 403, message = "无权访问"),
            @ApiResponse(code = 404, message = "记录不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> getInterviewRecord(
            @ApiParam(value = "面试记录ID", required = true, example = "INTERVIEW_1691234567890_123")
            @PathVariable String interviewId,
            HttpServletRequest request) {

        try {
            // 验证用户身份
            String token = jwtUtil.getTokenFromRequest(request);
            if (token == null) {
                return error(401, "未找到认证令牌");
            }

            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return error(401, "认证令牌无效");
            }

            // 查询面试记录
            InterviewRecord interviewRecord = interviewRecordService.getByInterviewId(interviewId);
            if (interviewRecord == null) {
                return error(404, "面试记录不存在");
            }

            // 验证权限
            if (!userId.equals(interviewRecord.getUserId())) {
                return error(403, "无权访问此面试记录");
            }

            return success("获取面试记录成功", interviewRecord);

        } catch (Exception e) {
            log.error("【获取面试记录异常】面试ID: {}, 错误: {}", interviewId, e.getMessage(), e);
            return error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 获取用户面试记录列表
     */
    @GetMapping("/list")
    @ApiOperation(
            value = "获取用户面试记录列表",
            notes = "分页获取当前用户的面试记录"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 401, message = "未授权"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> getUserInterviewRecords(
            @ApiParam(value = "当前页", required = false, example = "1")
            @RequestParam(defaultValue = "1") long current,
            @ApiParam(value = "页大小", required = false, example = "10")
            @RequestParam(defaultValue = "10") long size,
            HttpServletRequest request) {

        try {
            // 验证用户身份
            String token = jwtUtil.getTokenFromRequest(request);
            if (token == null) {
                return error(401, "未找到认证令牌");
            }

            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return error(401, "认证令牌无效");
            }

            // 查询面试记录
            Page<InterviewRecord> page = interviewRecordService.getUserInterviewRecords(userId, current, size);

            Map<String, Object> data = new HashMap<>();
            data.put("records", page.getRecords());
            data.put("total", page.getTotal());
            data.put("current", page.getCurrent());
            data.put("size", page.getSize());
            data.put("pages", page.getPages());

            return success("获取面试记录列表成功", data);

        } catch (Exception e) {
            log.error("【获取面试记录列表异常】错误: {}", e.getMessage(), e);
            return error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 获取面试统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(
            value = "获取面试统计信息",
            notes = "获取当前用户的面试统计数据"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 401, message = "未授权"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> getInterviewStatistics(HttpServletRequest request) {

        try {
            // 验证用户身份
            String token = jwtUtil.getTokenFromRequest(request);
            if (token == null) {
                return error(401, "未找到认证令牌");
            }

            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return error(401, "认证令牌无效");
            }

            // 获取统计信息
            Map<String, Object> statistics = interviewRecordService.getInterviewStatistics(userId);

            return success("获取面试统计成功", statistics);

        } catch (Exception e) {
            log.error("【获取面试统计异常】错误: {}", e.getMessage(), e);
            return error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 系统健康检查接口
     */
    @GetMapping("/health")
    @ApiOperation(
            value = "系统健康检查",
            notes = "检查面试系统的运行状态"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "系统运行正常"),
            @ApiResponse(code = 500, message = "系统异常")
    })
    public ResponseEntity<Map<String, Object>> health() {
        log.info("【健康检查】系统状态检查请求");

        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "JobPlusV8 面试系统");
        response.put("version", "v2.0.0");
        response.put("timestamp", LocalDateTime.now());
        response.put("description", "面试系统运行正常，WebSocket服务可用");

        return success("系统健康检查通过", response);
    }
}
