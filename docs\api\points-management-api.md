# 积分管理 API 文档

## 概述

积分管理API提供积分查询、语音识别时长管理等功能。语音识别基于积分系统，1积分=1秒语音识别时长。

**Base URL**: `/api/speech/duration`

## 认证

所有API都需要JWT认证，请在请求头中包含：
```
Authorization: Bearer <your-jwt-token>
```

## API接口

### 1. 获取用户积分信息

获取当前用户的积分信息，用于语音识别功能。

**请求**
```http
GET /api/speech/duration/info
```

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "userId": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "currentPoints": 100,
    "remainingDuration": 100,
    "pointsPerSecond": 1
  }
}
```

**字段说明**
- `currentPoints`: 当前积分余额
- `remainingDuration`: 可用语音识别时长（秒）
- `pointsPerSecond`: 每秒消耗的积分数（固定为1）

### 2. 检查积分是否充足

检查当前用户是否有足够的积分进行语音识别。

**请求**
```http
GET /api/speech/duration/check
```

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "hasEnoughDuration": true,
    "currentPoints": 100,
    "remainingDuration": 100
  }
}
```

### 3. 获取指定用户积分信息

根据用户名获取指定用户的积分信息（需要管理员权限）。

**请求**
```http
GET /api/speech/duration/user/{username}
```

**路径参数**
- `username`: 用户名

**响应**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "userId": 2,
    "username": "targetuser",
    "email": "<EMAIL>",
    "currentPoints": 50,
    "remainingDuration": 50,
    "pointsPerSecond": 1
  }
}
```

## 积分消费规则

### 语音识别计费
- **计费单位**: 1秒 = 1积分
- **计费方式**: 按实际使用时长计费
- **最小计费**: 不足1秒按1秒计算
- **扣费时机**: 语音识别结束后扣除

### 其他功能计费
- **面试功能**: 10积分/次
- **AI答复**: 3积分/次

## 使用流程

### 1. 开始语音识别前
```http
GET /api/speech/duration/check
```
检查是否有足够积分

### 2. 语音识别过程中
系统会实时监控积分余额，如果积分不足会自动停止服务

### 3. 语音识别结束后
系统会根据实际使用时长自动扣除相应积分

### 4. 查看剩余积分
```http
GET /api/speech/duration/info
```
查看当前积分余额和可用时长

## 错误处理

### 积分不足
当用户积分不足时，系统会返回402错误：

```json
{
  "code": 402,
  "message": "积分不足，使用语音识别功能需要1积分，请先充值",
  "data": null,
  "details": {
    "functionName": "语音识别",
    "pointsRequired": 1,
    "consumptionType": 2
  }
}
```

### 用户不存在
```json
{
  "code": 404,
  "message": "用户不存在",
  "data": null
}
```

## 权限说明

### 用户权限
- 查看自己的积分信息
- 检查自己的积分是否充足

### 管理员权限
- 查看任意用户的积分信息
- 为用户增加积分（通过用户中心的管理员直充功能）

## 注意事项

1. **精确计费**: 语音识别按实际使用秒数计费，确保公平
2. **实时检查**: 系统会实时检查积分余额，防止超额使用
3. **原子操作**: 积分扣除采用数据库事务，确保数据一致性
4. **错误恢复**: 如果语音识别失败，系统会考虑退还已扣除的积分

## 相关API

- [用户中心API](user-center-api.md) - 积分充值、充值记录查询
- [管理员API](admin-api.md) - 管理员直充、充值码管理
