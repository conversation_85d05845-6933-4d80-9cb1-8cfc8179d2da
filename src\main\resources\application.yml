# 应用服务 WEB 访问端口
server:
  port: 80

# Spring配置
spring:
  main:
    allow-circular-references: false  # 禁用循环引用（已通过重构解决）
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************
    username: root
    password: lipanpan
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,*************
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

  # Swagger配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher  # 解决Swagger与Spring Boot 2.6+兼容性问题

  # 邮件配置
  mail:
    host: smtp.qq.com  # QQ邮箱SMTP服务器
    port: 465  # SMTP端口
    username: <EMAIL>  # 发件人邮箱
    password: netewadnhixrbiai  # 邮箱授权码（需要替换为实际的授权码）
    from: <EMAIL>  # 发件人邮箱
    from-name: JobPlusV8面试系统  # 发件人显示名称
    code-expire-minutes: 5  # 验证码有效期（分钟）
    code-length: 6  # 验证码长度
    send-interval-seconds: 60  # 发送间隔（秒）
    properties:
      mail:
        smtp:
          auth: true  # 启用SMTP认证
          starttls:
            enable: true  # 启用TLS加密
            required: true  # 要求TLS加密
          ssl:
            trust: smtp.qq.com  # 信任的SMTP服务器
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.starttls.required: true
      mail.smtp.ssl.enable: true
    protocol: smtps

  # Redis配置
  redis:
    host: *************
    port: 6379
    password: lipanpan
    database: 0  # 使用数据库0
    timeout: 5000ms  # 连接超时时间
    lettuce:
      pool:
        max-active: 20  # 连接池最大连接数
        max-idle: 10    # 连接池最大空闲连接数
        min-idle: 5     # 连接池最小空闲连接数
        max-wait: 5000ms # 连接池最大阻塞等待时间

# MyBatis-Plus配置
mybatis-plus:
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:/mapper/**/*.xml
  # 搜索指定包别名
  type-aliases-package: cloud.ipanda.jobplusv8.entity
  configuration:
    # 开启驼峰命名
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: false
    # 设置关联对象选择延迟加载
    lazy-loading-enabled: true
    # 设置触发延迟加载的方法
    aggressive-lazy-loading: true
    # 允许或不允许多种结果集从一个单独的语句中返回
    multiple-result-sets-enabled: true
    # 使用列标签代替列名
    use-column-label: true
    # 允许JDBC 生成主键
    use-generated-keys: true
    # 指定 MyBatis 如何自动映射列到字段/属性
    auto-mapping-behavior: partial
    # 指定发现自动映射目标未知列（或者未知属性类型）的行为
    auto-mapping-unknown-column-behavior: warning
    # 配置默认的执行器
    default-executor-type: simple
    # 对象字段为null是否调用setter方法
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      # 字段策略 IGNORED:"忽略判断", NOT_NULL:"非 NULL 判断"), NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      # 驼峰下划线转换
      column-underline: true
      # 数据库大写下划线转换
      capital-mode: true
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false

# JWT配置
jwt:
  secret: jobplusv8-secret-key-for-jwt-token-generation-and-validation-must-be-at-least-256-bits
  expiration: 86400000  # 24小时（毫秒）

# Swagger文档配置
springfox:
  documentation:
    swagger-ui:
      enabled: true  # 启用Swagger UI
    open-api:
      v3:
        path: /v3/api-docs  # OpenAPI 3.0文档路径

# 腾讯云语音识别配置
tencent:
  cloud:
    secretId: AKIDeEgyCG9pLelfR7e0HSUL0Wrzuc7azLdq
    secret-key: 460lQ1XrytmuwoaBdx64rRpGYgeTdhZ8
    region: ap-beijing
    app-id: 1257395004

# 音频文件存储配置
audio:
  storage:
    path: ./audio-files  # 录音文件存储路径
    max-file-size: 104857600  # 单个文件最大大小（100MB）
    cleanup-days: 30  # 文件保留天数
    enable-compression: true  # 是否启用文件压缩

# 文件上传配置
file:
  upload:
    resume:
      path: e:\\upload  # 简历文件存储路径
      max-size: 20485760  # 单个文件最大大小（10MB）
      allowed-types: pdf,doc,docx,txt  # 允许的文件类型
      cleanup-days: 365  # 文件保留天数（1年）

# Gemini AI配置
gemini:
  enabled: true  # 是否启用Gemini AI解析
  #base-url: "http://*************:8000"  # Gemini API基础URL
  #api-key: "sk-lipanpan"  # API密钥
  base-url: "https://yunwu.zeabur.app"  # Gemini API基础URL
  api-key: "sk-sy3Ucct2XY3JCXN1pU8BSQ1XPX6h8QxlU4jOJKGN8UoOGGSE"  # API密钥
  model: "gemini-2.5-flash"  # 模型名称
  connect-timeout: 30000  # 连接超时时间（毫秒）
  read-timeout: 60000  # 读取超时时间（毫秒）
  temperature: 1.0  # 温度参数
  response-mime-type: "text/plain"  # 响应MIME类型
  thinking-budget: 0  # 思考预算