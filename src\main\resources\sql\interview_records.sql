-- 面试记录表
-- 用于存储用户的面试记录信息

-- 删除旧表（如果存在）
DROP TABLE IF EXISTS `interview_records`;

-- 创建面试记录表
CREATE TABLE `interview_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `interview_id` varchar(100) NOT NULL COMMENT '面试记录ID（唯一标识）',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `company_name` varchar(200) NOT NULL COMMENT '面试公司名称',
  `position` varchar(200) NOT NULL COMMENT '面试岗位',
  `websocket_session_id` varchar(100) NOT NULL COMMENT 'WebSocket会话ID',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '面试状态：1-准备中，2-进行中，3-已结束，4-超时结束，5-强制结束',
  `start_time` datetime DEFAULT NULL COMMENT '面试开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '面试结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '面试持续时长（秒）',
  `audio_file_path` varchar(500) DEFAULT NULL COMMENT '面试录音文件路径',
  `audio_file_size` bigint(20) DEFAULT NULL COMMENT '录音文件大小（字节）',
  `transcript_text` longtext COMMENT '面试转录文本',
  `total_words` int(11) DEFAULT NULL COMMENT '面试总字数',
  `interview_score` int(11) DEFAULT NULL COMMENT '面试评分（1-10分）',
  `interview_feedback` text COMMENT '面试评价/反馈',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理信息',
  `end_reason` varchar(50) DEFAULT NULL COMMENT '结束原因：NORMAL-正常结束，TIMEOUT-超时，FORCE-强制结束，ERROR-错误结束',
  `remarks` text COMMENT '面试备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_interview_id` (`interview_id`),
  UNIQUE KEY `uk_websocket_session_id` (`websocket_session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_company_name` (`company_name`),
  KEY `idx_position` (`position`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='面试记录表';

-- 插入测试数据
INSERT INTO `interview_records` (
  `interview_id`, `user_id`, `username`, `company_name`, `position`, 
  `websocket_session_id`, `status`, `start_time`, `end_time`, `duration`,
  `transcript_text`, `total_words`, `interview_score`, `interview_feedback`,
  `client_ip`, `user_agent`, `end_reason`, `remarks`
) VALUES 
(
  'INTERVIEW_1691234567890_1', 1, 'testuser', '阿里巴巴', 'Java开发工程师',
  'ws_session_001', 3, '2025-08-05 09:00:00', '2025-08-05 10:30:00', 5400,
  '面试官：请介绍一下你的项目经验。候选人：我主要负责过电商系统的开发...', 1250, 8,
  '候选人技术基础扎实，项目经验丰富，表达清晰，建议通过。',
  '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'NORMAL',
  '表现优秀的候选人'
),
(
  'INTERVIEW_1691234567891_1', 1, 'testuser', '腾讯', '前端开发工程师',
  'ws_session_002', 3, '2025-08-04 14:00:00', '2025-08-04 15:15:00', 4500,
  '面试官：你对Vue.js有什么了解？候选人：Vue.js是一个渐进式框架...', 980, 7,
  '前端基础良好，但缺乏大型项目经验，需要进一步培养。',
  '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'NORMAL',
  '有潜力的候选人'
),
(
  'INTERVIEW_1691234567892_1', 1, 'testuser', '字节跳动', 'Python开发工程师',
  'ws_session_003', 1, NULL, NULL, NULL,
  NULL, NULL, NULL, NULL,
  '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', NULL,
  '准备中的面试'
);

-- 验证数据插入
SELECT 
  interview_id,
  company_name,
  position,
  status,
  CASE status
    WHEN 1 THEN '准备中'
    WHEN 2 THEN '进行中'
    WHEN 3 THEN '已结束'
    WHEN 4 THEN '超时结束'
    WHEN 5 THEN '强制结束'
    ELSE '未知状态'
  END as status_name,
  duration,
  interview_score,
  create_time
FROM interview_records 
WHERE deleted = 0
ORDER BY create_time DESC;

-- 统计查询示例
SELECT 
  COUNT(*) as total_interviews,
  COUNT(CASE WHEN status = 3 THEN 1 END) as completed_interviews,
  AVG(CASE WHEN interview_score IS NOT NULL THEN interview_score END) as avg_score,
  SUM(CASE WHEN duration IS NOT NULL THEN duration END) as total_duration_seconds
FROM interview_records 
WHERE user_id = 1 AND deleted = 0;
