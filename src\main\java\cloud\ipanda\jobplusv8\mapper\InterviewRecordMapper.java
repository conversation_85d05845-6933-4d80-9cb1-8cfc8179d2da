package cloud.ipanda.jobplusv8.mapper;

import cloud.ipanda.jobplusv8.entity.InterviewRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 面试记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface InterviewRecordMapper extends BaseMapper<InterviewRecord> {

    /**
     * 获取用户面试统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalCount, " +
            "COUNT(CASE WHEN status = 3 THEN 1 END) as completedCount, " +
            "COALESCE(SUM(duration), 0) as totalDuration, " +
            "COALESCE(AVG(interview_score), 0) as averageScore " +
            "FROM interview_records " +
            "WHERE user_id = #{userId} AND deleted = 0")
    Map<String, Object> getUserInterviewStatistics(@Param("userId") Long userId);

    /**
     * 获取用户最近的面试记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 面试记录列表
     */
    @Select("SELECT * FROM interview_records " +
            "WHERE user_id = #{userId} AND deleted = 0 " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    java.util.List<InterviewRecord> getUserRecentInterviews(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 根据公司名称统计面试次数
     * 
     * @param userId 用户ID
     * @return 公司面试统计
     */
    @Select("SELECT company_name, COUNT(*) as count " +
            "FROM interview_records " +
            "WHERE user_id = #{userId} AND deleted = 0 " +
            "GROUP BY company_name " +
            "ORDER BY count DESC")
    java.util.List<Map<String, Object>> getCompanyInterviewStats(@Param("userId") Long userId);

    /**
     * 根据岗位统计面试次数
     * 
     * @param userId 用户ID
     * @return 岗位面试统计
     */
    @Select("SELECT position, COUNT(*) as count " +
            "FROM interview_records " +
            "WHERE user_id = #{userId} AND deleted = 0 " +
            "GROUP BY position " +
            "ORDER BY count DESC")
    java.util.List<Map<String, Object>> getPositionInterviewStats(@Param("userId") Long userId);
}
