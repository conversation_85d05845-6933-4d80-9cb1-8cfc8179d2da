package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.Resume;
import cloud.ipanda.jobplusv8.mapper.ResumeMapper;
import cloud.ipanda.jobplusv8.service.GeminiService;
import cloud.ipanda.jobplusv8.service.ResumeAsyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;

/**
 * 简历异步处理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class ResumeAsyncServiceImpl implements ResumeAsyncService {

    @Autowired
    private ResumeMapper resumeMapper;

    @Autowired
    private GeminiService geminiService;
    
    @Override
    @Async("taskExecutor")
    public void parseResumeAsync(Long resumeId) {
        String taskId = "ASYNC_PARSE_" + System.currentTimeMillis();
        long startTime = System.currentTimeMillis();
        
        log.info("【异步解析简历开始】任务ID: {}, 简历ID: {}, 线程: {}", 
                taskId, resumeId, Thread.currentThread().getName());

        Resume resume = resumeMapper.selectById(resumeId);
        if (resume == null) {
            log.error("【异步解析简历失败】任务ID: {}, 简历不存在，ID: {}", taskId, resumeId);
            return;
        }

        try {
            // 更新解析状态为解析中
            resume.setParseStatus(Resume.ParseStatus.PARSING);
            resumeMapper.updateById(resume);
            log.info("【异步解析简历】任务ID: {}, 状态更新为解析中", taskId);

            // 调用Gemini解析 - 使用新的Base64方式
            File file = new File(resume.getFilePath());
            if (!file.exists()) {
                throw new RuntimeException("简历文件不存在: " + resume.getFilePath());
            }

            log.info("【异步解析简历】任务ID: {}, 开始解析文件: {}, 大小: {}KB", 
                    taskId, file.getName(), file.length() / 1024);

            String parsedContent = geminiService.parseResume(file);

            if (parsedContent != null && !parsedContent.trim().isEmpty()) {
                // 解析成功
                resume.setParseStatus(Resume.ParseStatus.PARSED);
                resume.setParsedContent(parsedContent);
                resume.setParseError(null);
                
                long duration = System.currentTimeMillis() - startTime;
                log.info("【异步解析简历成功】任务ID: {}, 简历ID: {}, 解析结果长度: {}, 总耗时: {}ms, 线程: {}", 
                        taskId, resumeId, parsedContent.length(), duration, Thread.currentThread().getName());
            } else {
                // 解析失败
                resume.setParseStatus(Resume.ParseStatus.PARSE_FAILED);
                resume.setParseError("解析结果为空");
                
                long duration = System.currentTimeMillis() - startTime;
                log.warn("【异步解析简历失败】任务ID: {}, 简历ID: {}, 原因: 解析结果为空, 耗时: {}ms, 线程: {}", 
                        taskId, resumeId, duration, Thread.currentThread().getName());
            }

            resume.setParsedAt(LocalDateTime.now());
            resumeMapper.updateById(resume);

            long totalDuration = System.currentTimeMillis() - startTime;
            log.info("【异步解析简历完成】任务ID: {}, 简历ID: {}, 状态: {}, 总耗时: {}ms, 线程: {}",
                    taskId, resumeId, resume.getParseStatus(), totalDuration, Thread.currentThread().getName());

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("【异步解析简历异常】任务ID: {}, 简历ID: {}, 耗时: {}ms, 线程: {}, 错误: {}",
                    taskId, resumeId, duration, Thread.currentThread().getName(), e.getMessage(), e);

            // 更新解析状态为失败
            resume.setParseStatus(Resume.ParseStatus.PARSE_FAILED);
            resume.setParseError(e.getMessage());
            resume.setParsedAt(LocalDateTime.now());
            resumeMapper.updateById(resume);
        }
    }
}
