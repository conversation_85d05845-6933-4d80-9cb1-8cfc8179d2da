# 面试记录系统功能说明

## 🎯 功能概述

将原有的SpeechSession（语音会话）改造为InterviewRecord（面试记录），用户可以输入面试公司、面试岗位创建面试记录，获得面试记录ID和WebSocket会话ID，然后进入面试会议室建立WebSocket连接。

## 📋 核心功能

### 1. 面试记录创建
- **输入信息**: 面试公司名称、面试岗位
- **生成内容**: 
  - 面试记录ID（格式：`INTERVIEW_{timestamp}_{userId}`）
  - WebSocket会话ID（UUID格式）
  - WebSocket连接URL

### 2. 面试会议室
- **WebSocket端点**: `/ws/interview/meeting/{websocketSessionId}`
- **功能支持**:
  - 实时音频流传输
  - 文本消息通信
  - 面试状态管理
  - 会话生命周期控制

### 3. 面试记录管理
- **状态管理**: 准备中 → 进行中 → 已结束
- **数据记录**: 时长、转录文本、评分、反馈
- **历史查询**: 分页查询用户面试记录
- **统计分析**: 面试次数、平均评分、总时长

## 🏗️ 系统架构

### 实体类设计
```java
@TableName("interview_records")
public class InterviewRecord {
    private Long id;                    // 主键ID
    private String interviewId;         // 面试记录ID
    private Long userId;                // 用户ID
    private String username;            // 用户名
    private String companyName;         // 面试公司
    private String position;            // 面试岗位
    private String websocketSessionId;  // WebSocket会话ID
    private Integer status;             // 面试状态
    private LocalDateTime startTime;    // 开始时间
    private LocalDateTime endTime;      // 结束时间
    private Long duration;              // 持续时长（秒）
    private String transcriptText;      // 转录文本
    private Integer interviewScore;     // 面试评分
    private String interviewFeedback;   // 面试反馈
    // ... 其他字段
}
```

### 状态枚举
```java
public static class Status {
    public static final int PREPARING = 1;   // 准备中
    public static final int IN_PROGRESS = 2; // 进行中
    public static final int ENDED = 3;       // 已结束
    public static final int TIMEOUT = 4;     // 超时结束
    public static final int FORCED = 5;      // 强制结束
}
```

## 🔌 API接口

### 1. 创建面试记录
```http
POST /api/interview/create
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {jwt_token}

companyName=阿里巴巴&position=Java开发工程师
```

**响应示例**:
```json
{
  "success": true,
  "message": "面试记录创建成功",
  "data": {
    "interviewId": "INTERVIEW_1691234567890_123",
    "websocketSessionId": "a1b2c3d4e5f6g7h8i9j0",
    "companyName": "阿里巴巴",
    "position": "Java开发工程师",
    "status": 1,
    "createTime": "2025-08-05T14:30:00",
    "websocketUrl": "ws://localhost:80/ws/interview/meeting/a1b2c3d4e5f6g7h8i9j0"
  }
}
```

### 2. 获取面试记录详情
```http
GET /api/interview/{interviewId}
Authorization: Bearer {jwt_token}
```

### 3. 获取面试记录列表
```http
GET /api/interview/list?current=1&size=10
Authorization: Bearer {jwt_token}
```

### 4. 获取面试统计信息
```http
GET /api/interview/statistics
Authorization: Bearer {jwt_token}
```

## 🌐 WebSocket通信

### 连接建立
```javascript
const websocket = new WebSocket('ws://localhost:80/ws/interview/meeting/a1b2c3d4e5f6g7h8i9j0');

websocket.onopen = function(event) {
    console.log('面试会议室连接成功');
};

websocket.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};
```

### 消息格式
```json
{
  "type": "CONNECTION_SUCCESS",
  "message": "面试会议室连接成功",
  "timestamp": "2025-08-05T14:30:00",
  "data": {
    "interviewId": "INTERVIEW_1691234567890_123",
    "companyName": "阿里巴巴",
    "position": "Java开发工程师"
  }
}
```

### 音频数据传输
```javascript
// 发送音频数据
websocket.send(audioBuffer);
```

## 💾 数据库设计

### 表结构
```sql
CREATE TABLE `interview_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `interview_id` varchar(100) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `username` varchar(50) NOT NULL,
  `company_name` varchar(200) NOT NULL,
  `position` varchar(200) NOT NULL,
  `websocket_session_id` varchar(100) NOT NULL,
  `status` int(11) NOT NULL DEFAULT '1',
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `duration` bigint(20) DEFAULT NULL,
  `transcript_text` longtext,
  `total_words` int(11) DEFAULT NULL,
  `interview_score` int(11) DEFAULT NULL,
  `interview_feedback` text,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_interview_id` (`interview_id`),
  UNIQUE KEY `uk_websocket_session_id` (`websocket_session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 索引设计
- `uk_interview_id`: 面试记录ID唯一索引
- `uk_websocket_session_id`: WebSocket会话ID唯一索引
- `idx_user_id`: 用户ID索引
- `idx_company_name`: 公司名称索引
- `idx_position`: 岗位索引
- `idx_status`: 状态索引

## 🔄 业务流程

### 1. 创建面试记录
```mermaid
sequenceDiagram
    participant User as 用户
    participant API as API服务
    participant DB as 数据库
    
    User->>API: POST /api/interview/create
    API->>API: 验证JWT令牌
    API->>API: 生成面试记录ID和WebSocket会话ID
    API->>DB: 保存面试记录
    DB-->>API: 返回保存结果
    API-->>User: 返回面试记录信息
```

### 2. 进入面试会议室
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant WS as WebSocket服务
    participant Service as 业务服务
    participant DB as 数据库
    
    Client->>WS: 建立WebSocket连接
    WS->>Service: 验证面试记录
    Service->>DB: 查询面试记录
    DB-->>Service: 返回记录信息
    Service->>DB: 更新状态为"进行中"
    WS-->>Client: 发送连接成功消息
```

### 3. 面试结束
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant WS as WebSocket服务
    participant Service as 业务服务
    participant DB as 数据库
    
    Client->>WS: 关闭WebSocket连接
    WS->>Service: 处理连接关闭
    Service->>Service: 计算面试时长
    Service->>DB: 更新面试状态为"已结束"
    DB-->>Service: 返回更新结果
```

## 🚀 使用示例

### 前端集成示例
```javascript
// 1. 创建面试记录
async function createInterview(companyName, position) {
    const response = await fetch('/api/interview/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Bearer ${token}`
        },
        body: `companyName=${encodeURIComponent(companyName)}&position=${encodeURIComponent(position)}`
    });
    
    const result = await response.json();
    if (result.success) {
        return result.data;
    }
    throw new Error(result.message);
}

// 2. 连接面试会议室
function connectToInterviewRoom(websocketSessionId) {
    const websocket = new WebSocket(`ws://localhost:80/ws/interview/meeting/${websocketSessionId}`);
    
    websocket.onopen = function(event) {
        console.log('面试会议室连接成功');
    };
    
    websocket.onmessage = function(event) {
        const message = JSON.parse(event.data);
        handleInterviewMessage(message);
    };
    
    websocket.onclose = function(event) {
        console.log('面试会议室连接关闭');
    };
    
    return websocket;
}

// 3. 完整流程
async function startInterview(companyName, position) {
    try {
        // 创建面试记录
        const interviewData = await createInterview(companyName, position);
        console.log('面试记录创建成功:', interviewData);
        
        // 连接面试会议室
        const websocket = connectToInterviewRoom(interviewData.websocketSessionId);
        
        return {
            interviewId: interviewData.interviewId,
            websocket: websocket
        };
    } catch (error) {
        console.error('启动面试失败:', error);
        throw error;
    }
}
```

## 📊 监控和统计

### 面试统计指标
- **总面试次数**: 用户参与的面试总数
- **完成面试次数**: 状态为"已结束"的面试数
- **平均面试时长**: 所有完成面试的平均时长
- **平均面试评分**: 所有有评分面试的平均分
- **公司分布**: 按公司统计面试次数
- **岗位分布**: 按岗位统计面试次数

### 系统监控
- **活跃WebSocket连接数**: 当前正在进行的面试数量
- **面试成功率**: 正常结束的面试占比
- **平均连接时长**: WebSocket连接的平均持续时间

## 🔧 技术特点

### 1. 高并发支持
- 使用ConcurrentHashMap管理WebSocket会话
- 异步处理音频数据和消息
- 数据库连接池优化

### 2. 数据一致性
- 事务管理确保数据一致性
- 乐观锁防止并发更新冲突
- 逻辑删除保证数据完整性

### 3. 扩展性设计
- 模块化的服务层设计
- 可插拔的音频处理组件
- 支持多种消息类型扩展

### 4. 安全性保障
- JWT令牌验证
- 用户权限检查
- WebSocket连接验证

---

**创建时间**: 2025年8月5日  
**版本**: v1.0  
**维护者**: JobPlusV8 开发团队
