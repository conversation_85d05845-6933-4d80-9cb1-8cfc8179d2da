# 面试记录API使用示例

## 📋 概述

本文档提供面试记录API的完整使用示例，包括前端JavaScript代码、后端API调用和WebSocket通信。

## 🚀 完整面试流程示例

### 1. 前端JavaScript完整实现

```javascript
class InterviewManager {
    constructor(baseUrl = 'http://localhost:80', wsUrl = 'ws://localhost:80') {
        this.baseUrl = baseUrl;
        this.wsUrl = wsUrl;
        this.token = localStorage.getItem('jwt_token');
        this.currentInterview = null;
        this.websocket = null;
        this.audioRecorder = null;
    }

    // 设置JWT Token
    setToken(token) {
        this.token = token;
        localStorage.setItem('jwt_token', token);
    }

    // 创建面试记录
    async createInterview(companyName, position) {
        try {
            const response = await fetch(`${this.baseUrl}/api/interview/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': `Bearer ${this.token}`
                },
                body: `companyName=${encodeURIComponent(companyName)}&position=${encodeURIComponent(position)}`
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.currentInterview = result.data;
                console.log('面试记录创建成功:', this.currentInterview);
                return this.currentInterview;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('创建面试记录失败:', error);
            throw error;
        }
    }

    // 连接面试会议室
    async connectToMeetingRoom(websocketSessionId) {
        return new Promise((resolve, reject) => {
            const wsUrl = `${this.wsUrl}/ws/interview/meeting/${websocketSessionId}`;
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = (event) => {
                console.log('面试会议室连接成功');
                resolve(event);
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            };

            this.websocket.onclose = (event) => {
                console.log('面试会议室连接关闭:', event.reason);
                this.stopRecording();
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket连接错误:', error);
                reject(error);
            };

            // 连接超时处理
            setTimeout(() => {
                if (this.websocket.readyState === WebSocket.CONNECTING) {
                    this.websocket.close();
                    reject(new Error('WebSocket连接超时'));
                }
            }, 10000);
        });
    }

    // 处理WebSocket消息
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'CONNECTION_SUCCESS':
                console.log('面试开始:', message.data);
                this.onInterviewStarted && this.onInterviewStarted(message.data);
                break;
            case 'MESSAGE_RECEIVED':
                console.log('消息确认:', message.message);
                break;
            default:
                console.log('收到未知消息:', message);
        }
    }

    // 开始录音
    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            this.audioRecorder = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.audioRecorder.ondataavailable = (event) => {
                if (event.data.size > 0 && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                    this.websocket.send(event.data);
                }
            };

            this.audioRecorder.start(100); // 每100ms发送一次数据
            console.log('开始录音');
            
        } catch (error) {
            console.error('启动录音失败:', error);
            throw error;
        }
    }

    // 停止录音
    stopRecording() {
        if (this.audioRecorder && this.audioRecorder.state === 'recording') {
            this.audioRecorder.stop();
            console.log('停止录音');
        }
    }

    // 发送文本消息
    sendMessage(text) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(text);
        } else {
            console.warn('WebSocket未连接，无法发送消息');
        }
    }

    // 结束面试
    endInterview() {
        this.stopRecording();
        if (this.websocket) {
            this.websocket.close();
        }
    }

    // 获取面试记录详情
    async getInterviewRecord(interviewId) {
        try {
            const response = await fetch(`${this.baseUrl}/api/interview/${interviewId}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取面试记录失败:', error);
            throw error;
        }
    }

    // 获取面试记录列表
    async getInterviewList(current = 1, size = 10) {
        try {
            const response = await fetch(`${this.baseUrl}/api/interview/list?current=${current}&size=${size}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取面试记录列表失败:', error);
            throw error;
        }
    }

    // 获取面试统计
    async getInterviewStatistics() {
        try {
            const response = await fetch(`${this.baseUrl}/api/interview/statistics`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取面试统计失败:', error);
            throw error;
        }
    }
}
```

### 2. 使用示例

```javascript
// 初始化面试管理器
const interviewManager = new InterviewManager();

// 设置JWT Token（从登录接口获得）
interviewManager.setToken('eyJhbGciOiJIUzI1NiJ9...');

// 开始面试的完整流程
async function startInterview() {
    try {
        // 1. 创建面试记录
        const interview = await interviewManager.createInterview('阿里巴巴', 'Java开发工程师');
        console.log('面试记录创建成功:', interview);

        // 2. 连接面试会议室
        await interviewManager.connectToMeetingRoom(interview.websocketSessionId);
        console.log('面试会议室连接成功');

        // 3. 开始录音
        await interviewManager.startRecording();
        console.log('开始录音');

        // 4. 发送测试消息
        interviewManager.sendMessage('面试开始，请介绍一下自己');

        // 5. 设置面试开始回调
        interviewManager.onInterviewStarted = (data) => {
            console.log('面试正式开始:', data);
            updateUI('面试进行中...');
        };

        return interview;

    } catch (error) {
        console.error('启动面试失败:', error);
        alert('启动面试失败: ' + error.message);
    }
}

// 结束面试
function endInterview() {
    interviewManager.endInterview();
    console.log('面试已结束');
    updateUI('面试已结束');
}

// 查看面试历史
async function viewInterviewHistory() {
    try {
        const historyData = await interviewManager.getInterviewList(1, 10);
        console.log('面试历史:', historyData);
        
        // 显示面试记录列表
        displayInterviewList(historyData.records);
        
    } catch (error) {
        console.error('获取面试历史失败:', error);
    }
}

// 查看面试统计
async function viewInterviewStatistics() {
    try {
        const stats = await interviewManager.getInterviewStatistics();
        console.log('面试统计:', stats);
        
        // 显示统计信息
        displayStatistics(stats);
        
    } catch (error) {
        console.error('获取面试统计失败:', error);
    }
}
```

### 3. HTML界面示例

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面试系统</title>
    <style>
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .interview-list { margin-top: 20px; }
        .interview-item { padding: 10px; border: 1px solid #ddd; margin-bottom: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>面试系统</h1>
        
        <!-- 创建面试 -->
        <div class="section">
            <h2>创建面试</h2>
            <div class="form-group">
                <label for="companyName">面试公司:</label>
                <input type="text" id="companyName" placeholder="请输入面试公司名称">
            </div>
            <div class="form-group">
                <label for="position">面试岗位:</label>
                <input type="text" id="position" placeholder="请输入面试岗位">
            </div>
            <button class="btn" onclick="createAndStartInterview()">创建并开始面试</button>
            <button class="btn" onclick="endCurrentInterview()" id="endBtn" disabled>结束面试</button>
        </div>

        <!-- 状态显示 -->
        <div id="status" class="status" style="display: none;"></div>

        <!-- 面试历史 -->
        <div class="section">
            <h2>面试历史</h2>
            <button class="btn" onclick="loadInterviewHistory()">加载面试历史</button>
            <div id="interviewList" class="interview-list"></div>
        </div>

        <!-- 面试统计 -->
        <div class="section">
            <h2>面试统计</h2>
            <button class="btn" onclick="loadInterviewStatistics()">查看统计</button>
            <div id="statistics"></div>
        </div>
    </div>

    <script>
        // 这里引入上面的InterviewManager类
        const interviewManager = new InterviewManager();
        
        // 设置Token（实际应用中从登录获取）
        interviewManager.setToken('your-jwt-token-here');

        let currentInterview = null;

        // 创建并开始面试
        async function createAndStartInterview() {
            const companyName = document.getElementById('companyName').value;
            const position = document.getElementById('position').value;

            if (!companyName || !position) {
                showStatus('请填写完整的面试信息', 'error');
                return;
            }

            try {
                showStatus('正在创建面试记录...', 'success');
                
                currentInterview = await interviewManager.createInterview(companyName, position);
                
                showStatus('正在连接面试会议室...', 'success');
                await interviewManager.connectToMeetingRoom(currentInterview.websocketSessionId);
                
                showStatus('正在启动录音...', 'success');
                await interviewManager.startRecording();
                
                showStatus(`面试开始！公司: ${companyName}, 岗位: ${position}`, 'success');
                
                document.getElementById('endBtn').disabled = false;
                
            } catch (error) {
                showStatus('启动面试失败: ' + error.message, 'error');
            }
        }

        // 结束当前面试
        function endCurrentInterview() {
            if (currentInterview) {
                interviewManager.endInterview();
                showStatus('面试已结束', 'success');
                document.getElementById('endBtn').disabled = true;
                currentInterview = null;
            }
        }

        // 加载面试历史
        async function loadInterviewHistory() {
            try {
                const data = await interviewManager.getInterviewList();
                displayInterviewList(data.records);
            } catch (error) {
                showStatus('加载面试历史失败: ' + error.message, 'error');
            }
        }

        // 加载面试统计
        async function loadInterviewStatistics() {
            try {
                const stats = await interviewManager.getInterviewStatistics();
                displayStatistics(stats);
            } catch (error) {
                showStatus('加载面试统计失败: ' + error.message, 'error');
            }
        }

        // 显示状态信息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        // 显示面试记录列表
        function displayInterviewList(records) {
            const listDiv = document.getElementById('interviewList');
            listDiv.innerHTML = '';

            records.forEach(record => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'interview-item';
                itemDiv.innerHTML = `
                    <h4>${record.companyName} - ${record.position}</h4>
                    <p>状态: ${getStatusText(record.status)}</p>
                    <p>创建时间: ${record.createTime}</p>
                    ${record.duration ? `<p>面试时长: ${Math.floor(record.duration / 60)}分钟</p>` : ''}
                    ${record.interviewScore ? `<p>面试评分: ${record.interviewScore}分</p>` : ''}
                `;
                listDiv.appendChild(itemDiv);
            });
        }

        // 显示统计信息
        function displayStatistics(stats) {
            const statsDiv = document.getElementById('statistics');
            statsDiv.innerHTML = `
                <div class="interview-item">
                    <h4>面试统计</h4>
                    <p>总面试次数: ${stats.totalCount}</p>
                    <p>完成面试次数: ${stats.completedCount}</p>
                    <p>总面试时长: ${Math.floor(stats.totalDuration / 60)}分钟</p>
                    <p>平均评分: ${stats.averageScore}分</p>
                </div>
            `;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                1: '准备中',
                2: '进行中',
                3: '已结束',
                4: '超时结束',
                5: '强制结束'
            };
            return statusMap[status] || '未知状态';
        }
    </script>
</body>
</html>
```

## 🔧 错误处理示例

### 常见错误处理

```javascript
// 统一错误处理函数
function handleApiError(error, operation) {
    console.error(`${operation}失败:`, error);
    
    if (error.message.includes('401')) {
        // Token过期，需要重新登录
        alert('登录已过期，请重新登录');
        window.location.href = '/login.html';
    } else if (error.message.includes('403')) {
        // 权限不足
        alert('权限不足，无法执行此操作');
    } else if (error.message.includes('404')) {
        // 资源不存在
        alert('请求的资源不存在');
    } else if (error.message.includes('500')) {
        // 服务器错误
        alert('服务器错误，请稍后重试');
    } else {
        // 其他错误
        alert(`操作失败: ${error.message}`);
    }
}

// 在API调用中使用错误处理
async function safeCreateInterview(companyName, position) {
    try {
        return await interviewManager.createInterview(companyName, position);
    } catch (error) {
        handleApiError(error, '创建面试记录');
        throw error;
    }
}
```

### WebSocket重连机制

```javascript
class ReconnectingWebSocket {
    constructor(url, options = {}) {
        this.url = url;
        this.options = {
            maxReconnectAttempts: 5,
            reconnectInterval: 3000,
            ...options
        };
        this.reconnectAttempts = 0;
        this.connect();
    }

    connect() {
        this.websocket = new WebSocket(this.url);
        
        this.websocket.onopen = (event) => {
            console.log('WebSocket连接成功');
            this.reconnectAttempts = 0;
            this.onopen && this.onopen(event);
        };

        this.websocket.onmessage = (event) => {
            this.onmessage && this.onmessage(event);
        };

        this.websocket.onclose = (event) => {
            console.log('WebSocket连接关闭');
            this.onclose && this.onclose(event);
            
            if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
                this.reconnect();
            }
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.onerror && this.onerror(error);
        };
    }

    reconnect() {
        this.reconnectAttempts++;
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.connect();
        }, this.options.reconnectInterval);
    }

    send(data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(data);
        }
    }

    close() {
        if (this.websocket) {
            this.websocket.close();
        }
    }
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025年8月5日  
**维护者**: JobPlusV8 开发团队
