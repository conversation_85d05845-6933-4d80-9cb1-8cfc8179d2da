package cloud.ipanda.jobplusv8.service;

import cloud.ipanda.jobplusv8.entity.InterviewRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 面试记录服务接口
 * 
 * 功能说明：
 * 1. 面试记录的CRUD操作
 * 2. 面试会话的生命周期管理
 * 3. 面试状态的更新和查询
 * 4. 面试数据的统计和分析
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface InterviewRecordService extends IService<InterviewRecord> {

    /**
     * 创建面试记录
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param companyName 面试公司
     * @param position 面试岗位
     * @return 面试记录
     */
    InterviewRecord createInterviewRecord(Long userId, String username, String companyName, String position);

    /**
     * 根据面试记录ID查询
     * 
     * @param interviewId 面试记录ID
     * @return 面试记录
     */
    InterviewRecord getByInterviewId(String interviewId);

    /**
     * 根据WebSocket会话ID查询
     * 
     * @param websocketSessionId WebSocket会话ID
     * @return 面试记录
     */
    InterviewRecord getByWebsocketSessionId(String websocketSessionId);

    /**
     * 开始面试
     * 
     * @param interviewId 面试记录ID
     * @return 是否成功
     */
    boolean startInterview(String interviewId);

    /**
     * 结束面试
     * 
     * @param interviewId 面试记录ID
     * @param endReason 结束原因
     * @return 是否成功
     */
    boolean endInterview(String interviewId, String endReason);

    /**
     * 更新面试转录文本
     * 
     * @param interviewId 面试记录ID
     * @param transcriptText 转录文本
     * @return 是否成功
     */
    boolean updateTranscriptText(String interviewId, String transcriptText);

    /**
     * 更新面试评分和反馈
     * 
     * @param interviewId 面试记录ID
     * @param score 评分
     * @param feedback 反馈
     * @return 是否成功
     */
    boolean updateScoreAndFeedback(String interviewId, Integer score, String feedback);

    /**
     * 获取用户的面试记录列表
     * 
     * @param userId 用户ID
     * @param current 当前页
     * @param size 页大小
     * @return 分页结果
     */
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<InterviewRecord> getUserInterviewRecords(
            Long userId, long current, long size);

    /**
     * 获取面试统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> getInterviewStatistics(Long userId);
}
