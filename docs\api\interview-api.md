# 面试记录API文档

## 📋 API概览

面试记录系统提供完整的面试管理功能，包括创建面试记录、WebSocket会议室连接、历史记录查询和统计分析。

### 基础信息
- **Base URL**: `http://localhost:80/api/interview`
- **认证方式**: JWT Bearer Token
- **Content-Type**: `application/x-www-form-urlencoded` 或 `application/json`

### 认证说明
所有API接口都需要在请求头中携带JWT令牌：
```http
Authorization: Bearer {jwt_token}
```

## 🚀 API接口列表

### 1. 创建面试记录

创建新的面试记录，用户输入面试公司和岗位信息。

**接口信息**
- **URL**: `POST /api/interview/create`
- **描述**: 创建面试记录，返回面试记录ID和WebSocket会话ID

**请求参数**
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| companyName | String | 是 | 面试公司名称 | 阿里巴巴 |
| position | String | 是 | 面试岗位 | Java开发工程师 |

**请求示例**
```http
POST /api/interview/create
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...

companyName=阿里巴巴&position=Java开发工程师
```

**响应示例**
```json
{
  "code": 200,
  "message": "面试记录创建成功",
  "data": {
    "interviewId": "INTERVIEW_1691234567890_123",
    "websocketSessionId": "a1b2c3d4e5f6g7h8i9j0",
    "companyName": "阿里巴巴",
    "position": "Java开发工程师",
    "status": 1,
    "createTime": "2025-08-05T14:30:00",
    "websocketUrl": "ws://localhost:80/ws/interview/meeting/a1b2c3d4e5f6g7h8i9j0"
  }
}
```

**状态码说明**
- `200`: 创建成功
- `400`: 参数错误（公司名称或岗位为空）
- `401`: 未授权（JWT令牌无效）
- `500`: 服务器内部错误

### 2. 获取面试记录详情

根据面试记录ID获取详细信息。

**接口信息**
- **URL**: `GET /api/interview/{interviewId}`
- **描述**: 获取指定面试记录的详细信息

**路径参数**
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| interviewId | String | 是 | 面试记录ID | INTERVIEW_1691234567890_123 |

**请求示例**
```http
GET /api/interview/INTERVIEW_1691234567890_123
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

**响应示例**
```json
{
  "code": 200,
  "message": "获取面试记录成功",
  "data": {
    "id": 1,
    "interviewId": "INTERVIEW_1691234567890_123",
    "userId": 123,
    "username": "testuser",
    "companyName": "阿里巴巴",
    "position": "Java开发工程师",
    "websocketSessionId": "a1b2c3d4e5f6g7h8i9j0",
    "status": 3,
    "startTime": "2025-08-05T14:30:00",
    "endTime": "2025-08-05T16:00:00",
    "duration": 5400,
    "transcriptText": "面试官：请介绍一下你的项目经验...",
    "totalWords": 1250,
    "interviewScore": 8,
    "interviewFeedback": "候选人技术基础扎实，表达清晰",
    "createTime": "2025-08-05T14:30:00",
    "updateTime": "2025-08-05T16:00:00"
  }
}
```

**状态码说明**
- `200`: 查询成功
- `401`: 未授权
- `403`: 无权访问（不是本人的面试记录）
- `404`: 面试记录不存在
- `500`: 服务器内部错误

### 3. 获取面试记录列表

分页获取当前用户的面试记录列表。

**接口信息**
- **URL**: `GET /api/interview/list`
- **描述**: 分页查询当前用户的面试记录

**查询参数**
| 参数名 | 类型 | 必填 | 描述 | 默认值 | 示例 |
|--------|------|------|------|--------|------|
| current | Long | 否 | 当前页码 | 1 | 1 |
| size | Long | 否 | 每页大小 | 10 | 10 |

**请求示例**
```http
GET /api/interview/list?current=1&size=10
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

**响应示例**
```json
{
  "code": 200,
  "message": "获取面试记录列表成功",
  "data": {
    "records": [
      {
        "id": 1,
        "interviewId": "INTERVIEW_1691234567890_123",
        "companyName": "阿里巴巴",
        "position": "Java开发工程师",
        "status": 3,
        "startTime": "2025-08-05T14:30:00",
        "endTime": "2025-08-05T16:00:00",
        "duration": 5400,
        "interviewScore": 8,
        "createTime": "2025-08-05T14:30:00"
      },
      {
        "id": 2,
        "interviewId": "INTERVIEW_1691234567891_123",
        "companyName": "腾讯",
        "position": "前端开发工程师",
        "status": 3,
        "startTime": "2025-08-04T14:00:00",
        "endTime": "2025-08-04T15:15:00",
        "duration": 4500,
        "interviewScore": 7,
        "createTime": "2025-08-04T14:00:00"
      }
    ],
    "total": 15,
    "current": 1,
    "size": 10,
    "pages": 2
  }
}
```

**状态码说明**
- `200`: 查询成功
- `401`: 未授权
- `500`: 服务器内部错误

### 4. 获取面试统计信息

获取当前用户的面试统计数据。

**接口信息**
- **URL**: `GET /api/interview/statistics`
- **描述**: 获取用户面试统计信息

**请求示例**
```http
GET /api/interview/statistics
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

**响应示例**
```json
{
  "code": 200,
  "message": "获取面试统计成功",
  "data": {
    "totalCount": 15,
    "completedCount": 12,
    "totalDuration": 64800,
    "averageScore": 7.5,
    "completionRate": 80.0,
    "averageDuration": 5400
  }
}
```

**统计字段说明**
| 字段名 | 类型 | 描述 |
|--------|------|------|
| totalCount | Integer | 总面试次数 |
| completedCount | Integer | 已完成面试次数 |
| totalDuration | Long | 总面试时长（秒） |
| averageScore | Double | 平均面试评分 |
| completionRate | Double | 面试完成率（%） |
| averageDuration | Long | 平均面试时长（秒） |

**状态码说明**
- `200`: 查询成功
- `401`: 未授权
- `500`: 服务器内部错误

### 5. 系统健康检查

检查面试系统的运行状态。

**接口信息**
- **URL**: `GET /api/interview/health`
- **描述**: 系统健康检查，无需认证

**请求示例**
```http
GET /api/interview/health
```

**响应示例**
```json
{
  "code": 200,
  "message": "系统健康检查通过",
  "data": {
    "status": "UP",
    "service": "JobPlusV8 面试系统",
    "version": "v2.0.0",
    "timestamp": "2025-08-05T14:30:00",
    "description": "面试系统运行正常，WebSocket服务可用"
  }
}
```

## 🌐 WebSocket连接

### 连接信息
- **端点**: `ws://localhost:80/ws/interview/meeting/{websocketSessionId}`
- **协议**: WebSocket
- **认证**: 通过websocketSessionId验证

### 连接流程
1. 调用创建面试记录API获取websocketSessionId
2. 使用websocketSessionId建立WebSocket连接
3. 连接成功后自动更新面试状态为"进行中"
4. 可发送音频数据和文本消息
5. 连接断开时自动更新面试状态为"已结束"

### 消息格式

**连接成功消息**
```json
{
  "type": "CONNECTION_SUCCESS",
  "message": "面试会议室连接成功",
  "timestamp": "2025-08-05T14:30:00",
  "data": {
    "interviewId": "INTERVIEW_1691234567890_123",
    "companyName": "阿里巴巴",
    "position": "Java开发工程师"
  }
}
```

**消息确认**
```json
{
  "type": "MESSAGE_RECEIVED",
  "message": "消息已收到",
  "timestamp": "2025-08-05T14:30:00",
  "data": null
}
```

### JavaScript示例
```javascript
// 建立WebSocket连接
const websocket = new WebSocket('ws://localhost:80/ws/interview/meeting/a1b2c3d4e5f6g7h8i9j0');

websocket.onopen = function(event) {
    console.log('面试会议室连接成功');
};

websocket.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};

websocket.onclose = function(event) {
    console.log('面试会议室连接关闭');
};

// 发送文本消息
websocket.send('Hello, this is a test message');

// 发送音频数据
websocket.send(audioBuffer);
```

## 📊 数据模型

### 面试记录状态
| 状态值 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 准备中 | 面试记录已创建，等待开始 |
| 2 | 进行中 | 面试正在进行 |
| 3 | 已结束 | 面试正常结束 |
| 4 | 超时结束 | 面试因超时结束 |
| 5 | 强制结束 | 面试被强制结束 |

### 结束原因
| 原因代码 | 描述 |
|----------|------|
| NORMAL | 正常结束 |
| TIMEOUT | 超时结束 |
| FORCE | 强制结束 |
| ERROR | 错误结束 |
| NO_DURATION | 时长不足 |

## 🔧 错误处理

### 通用错误格式
```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码
| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权 | 检查JWT令牌是否有效 |
| 403 | 禁止访问 | 检查是否有权限访问该资源 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 500 | 服务器内部错误 | 联系系统管理员 |

## 📝 使用示例

### 完整面试流程示例

```javascript
// 1. 创建面试记录
async function createInterview() {
    const response = await fetch('/api/interview/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Bearer ${token}`
        },
        body: 'companyName=阿里巴巴&position=Java开发工程师'
    });
    
    const result = await response.json();
    if (result.code === 200) {
        return result.data;
    }
    throw new Error(result.message);
}

// 2. 连接面试会议室
function connectToMeeting(websocketSessionId) {
    const websocket = new WebSocket(`ws://localhost:80/ws/interview/meeting/${websocketSessionId}`);
    
    websocket.onopen = () => {
        console.log('面试开始');
    };
    
    websocket.onmessage = (event) => {
        const message = JSON.parse(event.data);
        handleMessage(message);
    };
    
    return websocket;
}

// 3. 查询面试记录
async function getInterviewRecord(interviewId) {
    const response = await fetch(`/api/interview/${interviewId}`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    
    return await response.json();
}

// 4. 完整流程
async function startInterview() {
    try {
        // 创建面试记录
        const interview = await createInterview();
        console.log('面试记录创建成功:', interview);
        
        // 连接面试会议室
        const websocket = connectToMeeting(interview.websocketSessionId);
        
        // 返回面试信息和连接
        return {
            interview,
            websocket
        };
    } catch (error) {
        console.error('启动面试失败:', error);
    }
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025年8月5日  
**维护者**: JobPlusV8 开发团队
