{"info": {"name": "JobPlusV8 面试记录API", "description": "JobPlusV8 面试记录系统API集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:80", "type": "string"}, {"key": "token", "value": "your-jwt-token-here", "type": "string"}], "item": [{"name": "面试记录管理", "item": [{"name": "创建面试记录", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "companyName", "value": "阿里巴巴", "type": "text"}, {"key": "position", "value": "Java开发工程师", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/interview/create", "host": ["{{baseUrl}}"], "path": ["api", "interview", "create"]}, "description": "创建新的面试记录，返回面试记录ID和WebSocket会话ID"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "companyName", "value": "阿里巴巴", "type": "text"}, {"key": "position", "value": "Java开发工程师", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/interview/create", "host": ["{{baseUrl}}"], "path": ["api", "interview", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 200,\n  \"message\": \"面试记录创建成功\",\n  \"data\": {\n    \"interviewId\": \"INTERVIEW_1691234567890_123\",\n    \"websocketSessionId\": \"a1b2c3d4e5f6g7h8i9j0\",\n    \"companyName\": \"阿里巴巴\",\n    \"position\": \"Java开发工程师\",\n    \"status\": 1,\n    \"createTime\": \"2025-08-05T14:30:00\",\n    \"websocketUrl\": \"ws://localhost:80/ws/interview/meeting/a1b2c3d4e5f6g7h8i9j0\"\n  }\n}"}]}, {"name": "获取面试记录详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/interview/INTERVIEW_1691234567890_123", "host": ["{{baseUrl}}"], "path": ["api", "interview", "INTERVIEW_1691234567890_123"]}, "description": "根据面试记录ID获取详细信息"}, "response": [{"name": "查询成功", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/interview/INTERVIEW_1691234567890_123", "host": ["{{baseUrl}}"], "path": ["api", "interview", "INTERVIEW_1691234567890_123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 200,\n  \"message\": \"获取面试记录成功\",\n  \"data\": {\n    \"id\": 1,\n    \"interviewId\": \"INTERVIEW_1691234567890_123\",\n    \"userId\": 123,\n    \"username\": \"testuser\",\n    \"companyName\": \"阿里巴巴\",\n    \"position\": \"Java开发工程师\",\n    \"websocketSessionId\": \"a1b2c3d4e5f6g7h8i9j0\",\n    \"status\": 3,\n    \"startTime\": \"2025-08-05T14:30:00\",\n    \"endTime\": \"2025-08-05T16:00:00\",\n    \"duration\": 5400,\n    \"transcriptText\": \"面试官：请介绍一下你的项目经验...\",\n    \"totalWords\": 1250,\n    \"interviewScore\": 8,\n    \"interviewFeedback\": \"候选人技术基础扎实，表达清晰\",\n    \"createTime\": \"2025-08-05T14:30:00\",\n    \"updateTime\": \"2025-08-05T16:00:00\"\n  }\n}"}]}, {"name": "获取面试记录列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/interview/list?current=1&size=10", "host": ["{{baseUrl}}"], "path": ["api", "interview", "list"], "query": [{"key": "current", "value": "1"}, {"key": "size", "value": "10"}]}, "description": "分页获取当前用户的面试记录列表"}, "response": [{"name": "查询成功", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/interview/list?current=1&size=10", "host": ["{{baseUrl}}"], "path": ["api", "interview", "list"], "query": [{"key": "current", "value": "1"}, {"key": "size", "value": "10"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 200,\n  \"message\": \"获取面试记录列表成功\",\n  \"data\": {\n    \"records\": [\n      {\n        \"id\": 1,\n        \"interviewId\": \"INTERVIEW_1691234567890_123\",\n        \"companyName\": \"阿里巴巴\",\n        \"position\": \"Java开发工程师\",\n        \"status\": 3,\n        \"startTime\": \"2025-08-05T14:30:00\",\n        \"endTime\": \"2025-08-05T16:00:00\",\n        \"duration\": 5400,\n        \"interviewScore\": 8,\n        \"createTime\": \"2025-08-05T14:30:00\"\n      }\n    ],\n    \"total\": 15,\n    \"current\": 1,\n    \"size\": 10,\n    \"pages\": 2\n  }\n}"}]}, {"name": "获取面试统计信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/interview/statistics", "host": ["{{baseUrl}}"], "path": ["api", "interview", "statistics"]}, "description": "获取当前用户的面试统计数据"}, "response": [{"name": "查询成功", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/interview/statistics", "host": ["{{baseUrl}}"], "path": ["api", "interview", "statistics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 200,\n  \"message\": \"获取面试统计成功\",\n  \"data\": {\n    \"totalCount\": 15,\n    \"completedCount\": 12,\n    \"totalDuration\": 64800,\n    \"averageScore\": 7.5,\n    \"completionRate\": 80.0,\n    \"averageDuration\": 5400\n  }\n}"}]}, {"name": "系统健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/interview/health", "host": ["{{baseUrl}}"], "path": ["api", "interview", "health"]}, "description": "检查面试系统的运行状态，无需认证"}, "response": [{"name": "系统正常", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/interview/health", "host": ["{{baseUrl}}"], "path": ["api", "interview", "health"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 200,\n  \"message\": \"系统健康检查通过\",\n  \"data\": {\n    \"status\": \"UP\",\n    \"service\": \"JobPlusV8 面试系统\",\n    \"version\": \"v2.0.0\",\n    \"timestamp\": \"2025-08-05T14:30:00\",\n    \"description\": \"面试系统运行正常，WebSocket服务可用\"\n  }\n}"}]}]}, {"name": "错误示例", "item": [{"name": "参数错误示例", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "companyName", "value": "", "type": "text"}, {"key": "position", "value": "", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/interview/create", "host": ["{{baseUrl}}"], "path": ["api", "interview", "create"]}, "description": "测试参数错误的情况"}, "response": [{"name": "参数错误", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "companyName", "value": "", "type": "text"}, {"key": "position", "value": "", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/interview/create", "host": ["{{baseUrl}}"], "path": ["api", "interview", "create"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 400,\n  \"message\": \"面试公司名称不能为空\",\n  \"data\": null\n}"}]}, {"name": "未授权示例", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/interview/list", "host": ["{{baseUrl}}"], "path": ["api", "interview", "list"]}, "description": "测试未提供JWT Token的情况"}, "response": [{"name": "未授权", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/interview/list", "host": ["{{baseUrl}}"], "path": ["api", "interview", "list"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": 401,\n  \"message\": \"未找到认证令牌\",\n  \"data\": null\n}"}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 可以在这里添加预请求脚本", "// 例如：自动刷新Token、设置时间戳等"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 通用测试脚本", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "});"]}}]}