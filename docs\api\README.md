# JobPlusV8 API 文档

## 📋 API 概览

JobPlusV8 提供完整的RESTful API，支持用户管理、积分系统、语音识别等功能。

### 🔗 API文档列表

| 文档 | 描述 | 主要功能 |
|------|------|----------|
| [面试记录API](interview-api.md) | 面试记录管理相关API | 创建面试记录、查询历史、统计分析 |
| [WebSocket面试会议室API](websocket-api.md) | 实时面试通信API | 音频传输、文本消息、状态管理 |
| [用户中心API](user-center-api.md) | 用户账号管理相关API | 个人信息、密码修改、积分查看、充值记录 |
| [积分管理API](points-management-api.md) | 积分查询和语音识别相关API | 积分查询、时长检查、语音识别计费 |
| [管理员API](admin-api.md) | 管理员功能相关API | 充值码管理、用户管理、统计查询 |

## 🌐 API基础信息

### Base URL
```
http://localhost:80
```

### 认证方式
所有API都使用JWT Bearer Token认证：
```http
Authorization: Bearer <your-jwt-token>
```

### 响应格式
所有API响应都采用统一格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { /* 响应数据 */ }
}
```

## 🔐 权限系统

### 用户权限
- `user:read` - 查看用户信息
- `user:write` - 修改用户信息

### 管理员权限
- `admin:read` - 查看管理数据
- `admin:write` - 管理操作

## 💰 积分系统规则

### 消费规则
- **面试功能**: 10积分/次
- **语音识别**: 1积分/秒
- **AI答复**: 3积分/次

### 充值方式
- **充值码充值**: 用户使用充值码自助充值
- **管理员直充**: 管理员为用户直接充值任意积分

## 🎯 快速开始

### 1. 获取JWT Token
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

### 2. 查看用户信息
```http
GET /api/user-center/info
Authorization: Bearer <your-jwt-token>
```

### 3. 检查积分余额
```http
GET /api/speech/duration/info
Authorization: Bearer <your-jwt-token>
```

### 4. 创建面试记录
```http
POST /api/interview/create
Authorization: Bearer <your-jwt-token>
Content-Type: application/x-www-form-urlencoded

companyName=阿里巴巴&position=Java开发工程师
```

### 5. 连接面试会议室
```javascript
// 使用返回的websocketSessionId连接WebSocket
const websocket = new WebSocket('ws://localhost:80/ws/interview/meeting/a1b2c3d4e5f6g7h8i9j0');
```

### 6. 使用充值码充值
```http
POST /api/user-center/recharge
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "rechargeCode": "RC20250804ABCD1234"
}
```

## 📊 状态码说明

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理响应数据 |
| 400 | 请求参数错误 | 检查请求参数格式和内容 |
| 401 | 未认证 | 检查JWT Token是否有效 |
| 402 | 积分不足 | 提示用户充值积分 |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 资源不存在 | 检查请求的资源ID |
| 500 | 服务器错误 | 联系技术支持 |

## 🔄 错误处理示例

### 积分不足错误
```json
{
  "code": 402,
  "message": "积分不足，使用语音识别功能需要1积分，请先充值",
  "data": null,
  "details": {
    "functionName": "语音识别",
    "pointsRequired": 1,
    "consumptionType": 2
  }
}
```

### 认证失败错误
```json
{
  "code": 401,
  "message": "Token已过期，请重新登录",
  "data": null
}
```

## 🧪 测试工具

### Swagger UI
访问在线API文档和测试工具：
```
http://localhost:80/swagger-ui.html
```

### Postman集合
可以导入以下环境变量：
```json
{
  "baseUrl": "http://localhost:80",
  "token": "your-jwt-token"
}
```

## 📝 开发注意事项

### 1. 分页查询
所有分页查询都支持以下参数：
- `current`: 页码，从1开始
- `size`: 每页大小，默认10

### 2. 时间格式
所有时间字段都使用ISO 8601格式：
```
2025-08-04T15:30:00
```

### 3. 金额格式
所有金额字段都使用decimal类型，保留2位小数：
```json
{
  "amount": 10.00
}
```

### 4. 积分计算
- 积分余额 = 可用语音识别秒数
- 1积分 = 1秒语音识别时长
- 积分扣除采用原子操作，确保数据一致性

## 🔗 相关链接

- [项目主页](../README.md)
- [数据库设计](../src/main/resources/sql/)
- [前端页面](../src/main/resources/static/)

---

**最后更新**: 2025年8月4日  
**API版本**: v1.0.0
