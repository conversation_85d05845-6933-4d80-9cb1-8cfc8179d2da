package cloud.ipanda.jobplusv8.websocket;

import cloud.ipanda.jobplusv8.entity.InterviewRecord;
import cloud.ipanda.jobplusv8.service.InterviewRecordService;
import cloud.ipanda.jobplusv8.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 面试会议室WebSocket端点
 *
 * 功能说明：
 * 1. 建立面试会议室的WebSocket连接
 * 2. 处理面试过程中的实时通信
 * 3. 管理面试会话的生命周期
 * 4. 支持音频流传输和文本消息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-05
 */
@Component
@Slf4j
@ServerEndpoint("/ws/interview/meeting/{websocketSessionId}")
public class JobplusInterviewMeetingWebsocket {

    /**
     * Spring应用上下文，用于获取Spring管理的Bean
     */
    private static ApplicationContext applicationContext;

    /**
     * 存储所有活跃的WebSocket会话
     * Key: websocketSessionId, Value: WebSocket Session
     */
    private static final ConcurrentHashMap<String, Session> activeSessions = new ConcurrentHashMap<>();

    /**
     * 存储会话对应的面试记录ID
     * Key: websocketSessionId, Value: interviewId
     */
    private static final ConcurrentHashMap<String, String> sessionInterviewMap = new ConcurrentHashMap<>();

    // Spring Bean依赖
    private JwtUtil jwtUtil;
    private InterviewRecordService interviewRecordService;
    private ObjectMapper objectMapper;


    /**
     * 建立面试会议室的连接通道
     *
     * @param websocketSessionId WebSocket会话ID
     * @param session WebSocket会话对象
     */
    @OnOpen
    public void onOpen(@PathParam("websocketSessionId") String websocketSessionId, Session session) {
        try {
            // 初始化依赖
            initializeDependencies();

            log.info("【面试会议室连接】WebSocket会话ID: {}, Session ID: {}", websocketSessionId, session.getId());

            // 验证面试记录是否存在
            InterviewRecord interviewRecord = interviewRecordService.getByWebsocketSessionId(websocketSessionId);
            if (interviewRecord == null) {
                log.error("【面试会议室连接失败】未找到对应的面试记录，WebSocket会话ID: {}", websocketSessionId);
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "面试记录不存在"));
                return;
            }

            // 检查面试状态
            if (!InterviewRecord.Status.PREPARING.equals(interviewRecord.getStatus()) &&
                !InterviewRecord.Status.IN_PROGRESS.equals(interviewRecord.getStatus())) {
                log.error("【面试会议室连接失败】面试状态不正确，当前状态: {}", interviewRecord.getStatus());
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "面试状态不正确"));
                return;
            }

            // 存储会话
            activeSessions.put(websocketSessionId, session);
            sessionInterviewMap.put(websocketSessionId, interviewRecord.getInterviewId());

            // 更新面试状态为进行中
            if (InterviewRecord.Status.PREPARING.equals(interviewRecord.getStatus())) {
                interviewRecord.setStatus(InterviewRecord.Status.IN_PROGRESS);
                interviewRecord.setStartTime(LocalDateTime.now());
                interviewRecordService.updateById(interviewRecord);
                log.info("【面试开始】面试记录ID: {}, 公司: {}, 岗位: {}",
                        interviewRecord.getInterviewId(), interviewRecord.getCompanyName(), interviewRecord.getPosition());
            }

            // 发送连接成功消息
            sendMessage(session, createMessage("CONNECTION_SUCCESS", "面试会议室连接成功", interviewRecord));

        } catch (Exception e) {
            log.error("【面试会议室连接异常】WebSocket会话ID: {}, 错误: {}", websocketSessionId, e.getMessage(), e);
            try {
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "连接异常"));
            } catch (IOException ioException) {
                log.error("【关闭WebSocket连接失败】", ioException);
            }
        }
    }

    /**
     * 接收消息
     *
     * @param message 消息内容
     * @param session WebSocket会话
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        String websocketSessionId = getWebsocketSessionId(session);
        log.info("【收到面试消息】WebSocket会话ID: {}, 消息: {}", websocketSessionId, message);

        try {
            // 这里可以处理不同类型的消息
            // 例如：心跳检测、状态更新、文本消息等

            // 回复确认消息
            sendMessage(session, createMessage("MESSAGE_RECEIVED", "消息已收到", null));

        } catch (Exception e) {
            log.error("【处理面试消息异常】WebSocket会话ID: {}, 错误: {}", websocketSessionId, e.getMessage(), e);
        }
    }

    /**
     * 接收二进制消息（音频数据）
     *
     * @param audioData 音频数据
     * @param session WebSocket会话
     */
    @OnMessage
    public void onBinaryMessage(byte[] audioData, Session session) {
        String websocketSessionId = getWebsocketSessionId(session);
        log.debug("【收到音频数据】WebSocket会话ID: {}, 数据大小: {} bytes", websocketSessionId, audioData.length);

        try {
            // 这里可以处理音频数据
            // 例如：保存音频、实时语音识别等

        } catch (Exception e) {
            log.error("【处理音频数据异常】WebSocket会话ID: {}, 错误: {}", websocketSessionId, e.getMessage(), e);
        }
    }


    /**
     * 连接关闭
     *
     * @param session WebSocket会话
     * @param closeReason 关闭原因
     */
    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        String websocketSessionId = getWebsocketSessionId(session);
        log.info("【面试会议室连接关闭】WebSocket会话ID: {}, 关闭原因: {}", websocketSessionId, closeReason.getReasonPhrase());

        try {
            // 清理会话
            activeSessions.remove(websocketSessionId);
            String interviewId = sessionInterviewMap.remove(websocketSessionId);

            // 更新面试记录状态
            if (interviewId != null && interviewRecordService != null) {
                InterviewRecord interviewRecord = interviewRecordService.getByInterviewId(interviewId);
                if (interviewRecord != null && InterviewRecord.Status.IN_PROGRESS.equals(interviewRecord.getStatus())) {
                    interviewRecord.setStatus(InterviewRecord.Status.ENDED);
                    interviewRecord.setEndTime(LocalDateTime.now());
                    interviewRecord.setEndReason(InterviewRecord.EndReason.NORMAL);

                    // 计算面试时长
                    if (interviewRecord.getStartTime() != null) {
                        long duration = java.time.Duration.between(interviewRecord.getStartTime(), interviewRecord.getEndTime()).getSeconds();
                        interviewRecord.setDuration(duration);
                    }

                    interviewRecordService.updateById(interviewRecord);
                    log.info("【面试结束】面试记录ID: {}, 时长: {}秒", interviewId, interviewRecord.getDuration());
                }
            }

        } catch (Exception e) {
            log.error("【面试会议室关闭异常】WebSocket会话ID: {}, 错误: {}", websocketSessionId, e.getMessage(), e);
        }
    }

    /**
     * 连接错误
     *
     * @param session WebSocket会话
     * @param throwable 错误信息
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        String websocketSessionId = getWebsocketSessionId(session);
        log.error("【面试会议室连接错误】WebSocket会话ID: {}, 错误: {}", websocketSessionId, throwable.getMessage(), throwable);

        try {
            // 强制关闭连接
            if (session.isOpen()) {
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "连接错误"));
            }
        } catch (IOException e) {
            log.error("【强制关闭WebSocket连接失败】", e);
        }
    }

    /**
     * 设置Spring应用上下文
     */
    public static void setApplicationContext(ApplicationContext context) {
        applicationContext = context;
    }

    /**
     * 初始化Spring Bean依赖
     */
    private void initializeDependencies() {
        if (applicationContext != null) {
            jwtUtil = applicationContext.getBean(JwtUtil.class);
            interviewRecordService = applicationContext.getBean(InterviewRecordService.class);
            objectMapper = applicationContext.getBean(ObjectMapper.class);
        }
    }

    /**
     * 发送消息到客户端
     *
     * @param session WebSocket会话
     * @param message 消息内容
     */
    private void sendMessage(Session session, String message) {
        try {
            if (session.isOpen()) {
                session.getBasicRemote().sendText(message);
            }
        } catch (IOException e) {
            log.error("【发送消息失败】错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建消息
     *
     * @param type 消息类型
     * @param message 消息内容
     * @param data 数据
     * @return JSON格式的消息
     */
    private String createMessage(String type, String message, Object data) {
        try {
            if (objectMapper == null) {
                return String.format("{\"type\":\"%s\",\"message\":\"%s\"}", type, message);
            }

            java.util.Map<String, Object> messageMap = new java.util.HashMap<>();
            messageMap.put("type", type);
            messageMap.put("message", message);
            messageMap.put("timestamp", LocalDateTime.now().toString());
            if (data != null) {
                messageMap.put("data", data);
            }

            return objectMapper.writeValueAsString(messageMap);
        } catch (Exception e) {
            log.error("【创建消息失败】错误: {}", e.getMessage(), e);
            return String.format("{\"type\":\"%s\",\"message\":\"%s\"}", type, message);
        }
    }

    /**
     * 从WebSocket会话中获取websocketSessionId
     *
     * @param session WebSocket会话
     * @return websocketSessionId
     */
    private String getWebsocketSessionId(Session session) {
        try {
            String pathInfo = session.getRequestURI().getPath();
            String[] pathParts = pathInfo.split("/");
            return pathParts[pathParts.length - 1];
        } catch (Exception e) {
            log.error("【获取WebSocket会话ID失败】错误: {}", e.getMessage(), e);
            return "unknown";
        }
    }

    /**
     * 广播消息到指定面试记录的所有连接
     *
     * @param interviewId 面试记录ID
     * @param message 消息内容
     */
    public static void broadcastToInterview(String interviewId, String message) {
        sessionInterviewMap.entrySet().stream()
                .filter(entry -> interviewId.equals(entry.getValue()))
                .forEach(entry -> {
                    Session session = activeSessions.get(entry.getKey());
                    if (session != null && session.isOpen()) {
                        try {
                            session.getBasicRemote().sendText(message);
                        } catch (IOException e) {
                            log.error("【广播消息失败】WebSocket会话ID: {}, 错误: {}", entry.getKey(), e.getMessage());
                        }
                    }
                });
    }

    /**
     * 获取当前活跃的会话数量
     *
     * @return 活跃会话数量
     */
    public static int getActiveSessionCount() {
        return activeSessions.size();
    }

}
