# WebSocket面试会议室API文档

## 📋 概述

面试会议室WebSocket API提供实时的面试通信功能，支持音频流传输、文本消息交换和面试状态管理。

### 基础信息
- **WebSocket端点**: `ws://localhost:80/ws/interview/meeting/{websocketSessionId}`
- **协议版本**: WebSocket 13
- **认证方式**: 通过websocketSessionId验证面试记录

## 🔌 连接管理

### 建立连接

**端点格式**
```
ws://localhost:80/ws/interview/meeting/{websocketSessionId}
```

**参数说明**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| websocketSessionId | String | 是 | 通过创建面试记录API获得的WebSocket会话ID |

**连接验证流程**
1. 验证websocketSessionId是否存在对应的面试记录
2. 检查面试记录状态（必须是"准备中"或"进行中"）
3. 验证面试记录归属权限
4. 更新面试状态为"进行中"（如果当前是"准备中"）
5. 发送连接成功确认消息

**连接成功响应**
```json
{
  "type": "CONNECTION_SUCCESS",
  "message": "面试会议室连接成功",
  "timestamp": "2025-08-05T14:30:00",
  "data": {
    "interviewId": "INTERVIEW_1691234567890_123",
    "companyName": "阿里巴巴",
    "position": "Java开发工程师",
    "status": 2,
    "startTime": "2025-08-05T14:30:00"
  }
}
```

### 连接失败情况

**面试记录不存在**
```
关闭码: 1003 (CANNOT_ACCEPT)
关闭原因: "面试记录不存在"
```

**面试状态不正确**
```
关闭码: 1003 (CANNOT_ACCEPT)
关闭原因: "面试状态不正确"
```

**系统异常**
```
关闭码: 1011 (UNEXPECTED_CONDITION)
关闭原因: "连接异常"
```

## 📨 消息类型

### 1. 文本消息

**发送文本消息**
```javascript
websocket.send("Hello, this is a text message");
```

**接收确认消息**
```json
{
  "type": "MESSAGE_RECEIVED",
  "message": "消息已收到",
  "timestamp": "2025-08-05T14:30:00",
  "data": null
}
```

### 2. 二进制消息（音频数据）

**发送音频数据**
```javascript
// 发送音频ArrayBuffer
websocket.send(audioBuffer);

// 发送音频Blob
websocket.send(audioBlob);
```

**音频数据处理**
- 支持的格式：PCM、WAV、MP3等
- 推荐采样率：16kHz
- 推荐位深：16bit
- 推荐声道：单声道

**音频数据日志**
```
DEBUG [Async-1] 【收到音频数据】WebSocket会话ID: a1b2c3d4e5f6g7h8i9j0, 数据大小: 1024 bytes
```

## 🔄 连接生命周期

### 连接建立 (onOpen)

**JavaScript示例**
```javascript
websocket.onopen = function(event) {
    console.log('面试会议室连接成功');
    
    // 可以开始发送消息和音频数据
    websocket.send('面试开始');
};
```

**服务端处理**
1. 验证面试记录
2. 更新面试状态
3. 记录连接日志
4. 发送连接成功消息

### 消息处理 (onMessage)

**JavaScript示例**
```javascript
websocket.onmessage = function(event) {
    if (typeof event.data === 'string') {
        // 处理文本消息
        const message = JSON.parse(event.data);
        handleTextMessage(message);
    } else {
        // 处理二进制数据（如果服务端发送）
        handleBinaryData(event.data);
    }
};

function handleTextMessage(message) {
    switch (message.type) {
        case 'CONNECTION_SUCCESS':
            console.log('连接成功:', message.data);
            break;
        case 'MESSAGE_RECEIVED':
            console.log('消息确认:', message.message);
            break;
        default:
            console.log('未知消息类型:', message);
    }
}
```

### 连接关闭 (onClose)

**JavaScript示例**
```javascript
websocket.onclose = function(event) {
    console.log('面试会议室连接关闭');
    console.log('关闭码:', event.code);
    console.log('关闭原因:', event.reason);
    
    // 处理连接关闭后的逻辑
    handleConnectionClose(event);
};
```

**服务端处理**
1. 清理WebSocket会话
2. 更新面试状态为"已结束"
3. 计算面试时长
4. 记录结束日志

**自动状态更新**
```json
{
  "status": 3,
  "endTime": "2025-08-05T16:00:00",
  "duration": 5400,
  "endReason": "NORMAL"
}
```

### 错误处理 (onError)

**JavaScript示例**
```javascript
websocket.onerror = function(error) {
    console.error('WebSocket错误:', error);
    
    // 处理错误情况
    handleWebSocketError(error);
};
```

**常见错误情况**
- 网络连接中断
- 服务器异常
- 消息格式错误
- 权限验证失败

## 🎵 音频处理

### 音频录制示例

```javascript
// 获取麦克风权限
async function startAudioRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            }
        });
        
        const mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'audio/webm;codecs=opus'
        });
        
        mediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0 && websocket.readyState === WebSocket.OPEN) {
                // 发送音频数据到WebSocket
                websocket.send(event.data);
            }
        };
        
        // 每100ms发送一次音频数据
        mediaRecorder.start(100);
        
        return mediaRecorder;
    } catch (error) {
        console.error('获取麦克风权限失败:', error);
    }
}
```

### 音频流处理

```javascript
class AudioStreamProcessor {
    constructor(websocket) {
        this.websocket = websocket;
        this.audioContext = new AudioContext();
        this.isRecording = false;
    }
    
    async startRecording() {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const source = this.audioContext.createMediaStreamSource(stream);
        
        // 创建音频处理节点
        const processor = this.audioContext.createScriptProcessor(4096, 1, 1);
        
        processor.onaudioprocess = (event) => {
            if (this.isRecording) {
                const audioData = event.inputBuffer.getChannelData(0);
                const buffer = this.float32ToInt16(audioData);
                
                if (this.websocket.readyState === WebSocket.OPEN) {
                    this.websocket.send(buffer);
                }
            }
        };
        
        source.connect(processor);
        processor.connect(this.audioContext.destination);
        
        this.isRecording = true;
    }
    
    stopRecording() {
        this.isRecording = false;
    }
    
    float32ToInt16(float32Array) {
        const int16Array = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            int16Array[i] = Math.max(-32768, Math.min(32767, float32Array[i] * 32768));
        }
        return int16Array.buffer;
    }
}
```

## 📊 状态监控

### 连接状态检查

```javascript
function checkWebSocketStatus(websocket) {
    const states = {
        0: 'CONNECTING',
        1: 'OPEN',
        2: 'CLOSING',
        3: 'CLOSED'
    };
    
    console.log('WebSocket状态:', states[websocket.readyState]);
    return websocket.readyState;
}

// 定期检查连接状态
setInterval(() => {
    if (websocket) {
        checkWebSocketStatus(websocket);
    }
}, 5000);
```

### 心跳检测

```javascript
class WebSocketHeartbeat {
    constructor(websocket, interval = 30000) {
        this.websocket = websocket;
        this.interval = interval;
        this.heartbeatTimer = null;
        this.reconnectTimer = null;
    }
    
    start() {
        this.heartbeatTimer = setInterval(() => {
            if (this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'HEARTBEAT',
                    timestamp: Date.now()
                }));
            }
        }, this.interval);
    }
    
    stop() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    reconnect() {
        this.stop();
        this.reconnectTimer = setTimeout(() => {
            // 重新连接逻辑
            this.websocket = new WebSocket(this.websocket.url);
            this.start();
        }, 5000);
    }
}
```

## 🔧 错误处理和重连

### 自动重连机制

```javascript
class ReconnectingWebSocket {
    constructor(url, options = {}) {
        this.url = url;
        this.options = {
            maxReconnectAttempts: 5,
            reconnectInterval: 3000,
            ...options
        };
        this.reconnectAttempts = 0;
        this.websocket = null;
        
        this.connect();
    }
    
    connect() {
        this.websocket = new WebSocket(this.url);
        
        this.websocket.onopen = (event) => {
            console.log('WebSocket连接成功');
            this.reconnectAttempts = 0;
            this.onopen && this.onopen(event);
        };
        
        this.websocket.onmessage = (event) => {
            this.onmessage && this.onmessage(event);
        };
        
        this.websocket.onclose = (event) => {
            console.log('WebSocket连接关闭');
            this.onclose && this.onclose(event);
            
            if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
                this.reconnect();
            }
        };
        
        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.onerror && this.onerror(error);
        };
    }
    
    reconnect() {
        this.reconnectAttempts++;
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.connect();
        }, this.options.reconnectInterval);
    }
    
    send(data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(data);
        } else {
            console.warn('WebSocket未连接，无法发送消息');
        }
    }
    
    close() {
        if (this.websocket) {
            this.websocket.close();
        }
    }
}
```

## 📝 完整使用示例

### 面试会议室客户端

```javascript
class InterviewMeetingRoom {
    constructor(websocketSessionId) {
        this.websocketSessionId = websocketSessionId;
        this.websocket = null;
        this.audioProcessor = null;
        this.isRecording = false;
    }
    
    // 连接面试会议室
    async connect() {
        const url = `ws://localhost:80/ws/interview/meeting/${this.websocketSessionId}`;
        this.websocket = new ReconnectingWebSocket(url);
        
        this.websocket.onopen = (event) => {
            console.log('面试会议室连接成功');
            this.onConnected && this.onConnected(event);
        };
        
        this.websocket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.websocket.onclose = (event) => {
            console.log('面试会议室连接关闭');
            this.stopRecording();
            this.onDisconnected && this.onDisconnected(event);
        };
        
        this.websocket.onerror = (error) => {
            console.error('面试会议室连接错误:', error);
            this.onError && this.onError(error);
        };
    }
    
    // 处理消息
    handleMessage(message) {
        switch (message.type) {
            case 'CONNECTION_SUCCESS':
                console.log('面试开始:', message.data);
                break;
            case 'MESSAGE_RECEIVED':
                console.log('消息确认:', message.message);
                break;
            default:
                console.log('收到消息:', message);
        }
        
        this.onMessage && this.onMessage(message);
    }
    
    // 发送文本消息
    sendMessage(text) {
        if (this.websocket) {
            this.websocket.send(text);
        }
    }
    
    // 开始录音
    async startRecording() {
        if (!this.isRecording) {
            this.audioProcessor = new AudioStreamProcessor(this.websocket);
            await this.audioProcessor.startRecording();
            this.isRecording = true;
            console.log('开始录音');
        }
    }
    
    // 停止录音
    stopRecording() {
        if (this.isRecording && this.audioProcessor) {
            this.audioProcessor.stopRecording();
            this.isRecording = false;
            console.log('停止录音');
        }
    }
    
    // 断开连接
    disconnect() {
        this.stopRecording();
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 使用示例
async function startInterview() {
    // 1. 创建面试记录
    const interviewData = await createInterviewRecord('阿里巴巴', 'Java开发工程师');
    
    // 2. 连接面试会议室
    const meetingRoom = new InterviewMeetingRoom(interviewData.websocketSessionId);
    
    meetingRoom.onConnected = () => {
        console.log('面试开始，可以开始录音');
        meetingRoom.startRecording();
    };
    
    meetingRoom.onMessage = (message) => {
        console.log('收到面试消息:', message);
    };
    
    meetingRoom.onDisconnected = () => {
        console.log('面试结束');
    };
    
    await meetingRoom.connect();
    
    return meetingRoom;
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025年8月5日  
**维护者**: JobPlusV8 开发团队
