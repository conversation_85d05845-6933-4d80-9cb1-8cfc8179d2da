package cloud.ipanda.jobplusv8.service.impl;

import cloud.ipanda.jobplusv8.entity.InterviewRecord;
import cloud.ipanda.jobplusv8.mapper.InterviewRecordMapper;
import cloud.ipanda.jobplusv8.service.InterviewRecordService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 面试记录服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class InterviewRecordServiceImpl extends ServiceImpl<InterviewRecordMapper, InterviewRecord> 
        implements InterviewRecordService {

    @Override
    public InterviewRecord createInterviewRecord(Long userId, String username, String companyName, String position) {
        try {
            // 生成唯一的面试记录ID和WebSocket会话ID
            String interviewId = "INTERVIEW_" + System.currentTimeMillis() + "_" + userId;
            String websocketSessionId = UUID.randomUUID().toString().replace("-", "");
            
            InterviewRecord interviewRecord = new InterviewRecord()
                    .setInterviewId(interviewId)
                    .setUserId(userId)
                    .setUsername(username)
                    .setCompanyName(companyName)
                    .setPosition(position)
                    .setWebsocketSessionId(websocketSessionId)
                    .setStatus(InterviewRecord.Status.PREPARING)
                    .setCreateTime(LocalDateTime.now());
            
            boolean saved = save(interviewRecord);
            if (saved) {
                log.info("【创建面试记录成功】面试ID: {}, 用户: {}, 公司: {}, 岗位: {}, WebSocket会话ID: {}", 
                        interviewId, username, companyName, position, websocketSessionId);
                return interviewRecord;
            } else {
                log.error("【创建面试记录失败】用户: {}, 公司: {}, 岗位: {}", username, companyName, position);
                return null;
            }
        } catch (Exception e) {
            log.error("【创建面试记录异常】用户: {}, 公司: {}, 岗位: {}, 错误: {}", 
                    username, companyName, position, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public InterviewRecord getByInterviewId(String interviewId) {
        try {
            QueryWrapper<InterviewRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("interview_id", interviewId);
            return getOne(queryWrapper);
        } catch (Exception e) {
            log.error("【根据面试ID查询失败】面试ID: {}, 错误: {}", interviewId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public InterviewRecord getByWebsocketSessionId(String websocketSessionId) {
        try {
            QueryWrapper<InterviewRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("websocket_session_id", websocketSessionId);
            return getOne(queryWrapper);
        } catch (Exception e) {
            log.error("【根据WebSocket会话ID查询失败】WebSocket会话ID: {}, 错误: {}", 
                    websocketSessionId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean startInterview(String interviewId) {
        try {
            InterviewRecord interviewRecord = getByInterviewId(interviewId);
            if (interviewRecord == null) {
                log.error("【开始面试失败】面试记录不存在，面试ID: {}", interviewId);
                return false;
            }
            
            if (!InterviewRecord.Status.PREPARING.equals(interviewRecord.getStatus())) {
                log.error("【开始面试失败】面试状态不正确，当前状态: {}", interviewRecord.getStatus());
                return false;
            }
            
            interviewRecord.setStatus(InterviewRecord.Status.IN_PROGRESS);
            interviewRecord.setStartTime(LocalDateTime.now());
            
            boolean updated = updateById(interviewRecord);
            if (updated) {
                log.info("【面试开始成功】面试ID: {}", interviewId);
            }
            return updated;
        } catch (Exception e) {
            log.error("【开始面试异常】面试ID: {}, 错误: {}", interviewId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean endInterview(String interviewId, String endReason) {
        try {
            InterviewRecord interviewRecord = getByInterviewId(interviewId);
            if (interviewRecord == null) {
                log.error("【结束面试失败】面试记录不存在，面试ID: {}", interviewId);
                return false;
            }
            
            LocalDateTime endTime = LocalDateTime.now();
            interviewRecord.setStatus(InterviewRecord.Status.ENDED);
            interviewRecord.setEndTime(endTime);
            interviewRecord.setEndReason(endReason);
            
            // 计算面试时长
            if (interviewRecord.getStartTime() != null) {
                long duration = java.time.Duration.between(interviewRecord.getStartTime(), endTime).getSeconds();
                interviewRecord.setDuration(duration);
            }
            
            boolean updated = updateById(interviewRecord);
            if (updated) {
                log.info("【面试结束成功】面试ID: {}, 时长: {}秒, 结束原因: {}", 
                        interviewId, interviewRecord.getDuration(), endReason);
            }
            return updated;
        } catch (Exception e) {
            log.error("【结束面试异常】面试ID: {}, 错误: {}", interviewId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateTranscriptText(String interviewId, String transcriptText) {
        try {
            InterviewRecord interviewRecord = getByInterviewId(interviewId);
            if (interviewRecord == null) {
                log.error("【更新转录文本失败】面试记录不存在，面试ID: {}", interviewId);
                return false;
            }
            
            interviewRecord.setTranscriptText(transcriptText);
            if (transcriptText != null) {
                interviewRecord.setTotalWords(transcriptText.length());
            }
            
            boolean updated = updateById(interviewRecord);
            if (updated) {
                log.info("【更新转录文本成功】面试ID: {}, 文本长度: {}", interviewId, 
                        transcriptText != null ? transcriptText.length() : 0);
            }
            return updated;
        } catch (Exception e) {
            log.error("【更新转录文本异常】面试ID: {}, 错误: {}", interviewId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateScoreAndFeedback(String interviewId, Integer score, String feedback) {
        try {
            InterviewRecord interviewRecord = getByInterviewId(interviewId);
            if (interviewRecord == null) {
                log.error("【更新评分反馈失败】面试记录不存在，面试ID: {}", interviewId);
                return false;
            }
            
            interviewRecord.setInterviewScore(score);
            interviewRecord.setInterviewFeedback(feedback);
            
            boolean updated = updateById(interviewRecord);
            if (updated) {
                log.info("【更新评分反馈成功】面试ID: {}, 评分: {}", interviewId, score);
            }
            return updated;
        } catch (Exception e) {
            log.error("【更新评分反馈异常】面试ID: {}, 错误: {}", interviewId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Page<InterviewRecord> getUserInterviewRecords(Long userId, long current, long size) {
        try {
            Page<InterviewRecord> page = new Page<>(current, size);
            QueryWrapper<InterviewRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.orderByDesc("create_time");
            
            return page(page, queryWrapper);
        } catch (Exception e) {
            log.error("【查询用户面试记录异常】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return new Page<>(current, size);
        }
    }

    @Override
    public Map<String, Object> getInterviewStatistics(Long userId) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            QueryWrapper<InterviewRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            
            // 总面试次数
            long totalCount = count(queryWrapper);
            statistics.put("totalCount", totalCount);
            
            // 已完成面试次数
            QueryWrapper<InterviewRecord> completedWrapper = new QueryWrapper<>();
            completedWrapper.eq("user_id", userId);
            completedWrapper.eq("status", InterviewRecord.Status.ENDED);
            long completedCount = count(completedWrapper);
            statistics.put("completedCount", completedCount);
            
            // 总面试时长（分钟）
            // 这里需要在Mapper中实现自定义查询
            statistics.put("totalDuration", 0);
            
            // 平均评分
            statistics.put("averageScore", 0.0);
            
            return statistics;
        } catch (Exception e) {
            log.error("【获取面试统计异常】用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return new HashMap<>();
        }
    }
}
