package cloud.ipanda.jobplusv8;

import cloud.ipanda.jobplusv8.websocket.JobplusInterviewMeetingWebsocket;
import cloud.ipanda.jobplusv8.websocket.TencentASRWebsocket;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

/**
 * JobPlusV8 面试语音识别系统主启动类
 *
 * 功能说明：
 * 1. 启动Spring Boot应用程序
 * 2. 启用WebSocket支持，用于实时音频流传输
 * 3. 初始化WebSocket应用上下文，确保依赖注入正常工作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21
 */
@SpringBootApplication  // Spring Boot自动配置注解，启用自动配置、组件扫描等功能
@EnableWebSocket       // 启用WebSocket支持，允许创建WebSocket端点
@EnableAsync          // 启用异步处理支持
public class Jobplusv8Application {

    /**
     * 应用程序主入口方法
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 创建Spring Boot应用程序实例
        SpringApplication springApplication = new SpringApplication(Jobplusv8Application.class);

        // 启动应用程序并获取应用上下文
        ConfigurableApplicationContext context = springApplication.run(args);

        // 将Spring应用上下文传递给WebSocket类
        // 这是必要的，因为WebSocket端点不是Spring管理的Bean，需要手动获取依赖
        TencentASRWebsocket.setApplicationContext(context);

        JobplusInterviewMeetingWebsocket.setApplicationContext(context);
    }
}
