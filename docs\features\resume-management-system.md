# 简历管理系统

## 功能概述

JobPlusV8简历管理系统是一个完整的简历文件管理解决方案，支持多种文件格式上传、AI智能解析、文件管理等功能。

## 核心功能

### 1. 文件上传支持
- **支持格式**：PDF、Word（.doc/.docx）、TXT
- **上传方式**：拖拽上传、点击选择
- **文件限制**：单文件最大10MB
- **存储结构**：按日期和用户ID分层存储

### 2. AI智能解析
- **集成Gemini AI**：自动解析简历内容
- **结构化输出**：提取基本信息、教育背景、工作经历、技能等
- **异步处理**：上传后后台异步解析，不影响用户体验
- **解析状态**：实时跟踪解析进度和结果

### 3. 简历管理
- **列表查看**：分页显示用户所有简历
- **详情查看**：查看简历详细信息和解析结果
- **名称修改**：支持修改简历显示名称
- **默认设置**：支持设置默认简历
- **文件下载**：支持原文件下载
- **删除管理**：支持删除不需要的简历

### 4. 显示信息
- **简历名称**：姓名+岗位格式
- **上传时间**：精确到秒的上传时间
- **文件信息**：文件大小、类型、格式化显示
- **解析状态**：未解析/解析中/解析成功/解析失败
- **默认标识**：清晰标识默认简历

## 技术架构

### 后端技术栈
- **Spring Boot 2.x**：主框架
- **MyBatis Plus**：数据库ORM
- **Spring Security**：安全认证
- **JWT**：用户认证
- **Swagger**：API文档

### 文件处理
- **Apache PDFBox**：PDF文件文本提取
- **Apache POI**：Word文件文本提取
- **文件存储**：本地文件系统存储
- **路径管理**：按日期和用户分层

### AI集成
- **Gemini API**：Google AI文本分析
- **异步处理**：Spring @Async注解
- **错误处理**：完善的异常处理机制
- **重试机制**：支持解析失败重试

## API接口

### 简历上传
```http
POST /api/resume/upload
Content-Type: multipart/form-data

Parameters:
- resumeName: 简历名称（必填）
- file: 简历文件（必填）
- isDefault: 是否设为默认（可选）
```

### 简历列表
```http
GET /api/resume/list?current=1&size=10
Authorization: Bearer {token}
```

### 简历详情
```http
GET /api/resume/{id}
Authorization: Bearer {token}
```

### 更新简历
```http
PUT /api/resume/{id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "resumeName": "新的简历名称",
  "isDefault": true
}
```

### 删除简历
```http
DELETE /api/resume/{id}
Authorization: Bearer {token}
```

### 设置默认简历
```http
POST /api/resume/{id}/set-default
Authorization: Bearer {token}
```

### 下载简历
```http
GET /api/resume/{id}/download
Authorization: Bearer {token}
```

### 重新解析
```http
POST /api/resume/{id}/reparse
Authorization: Bearer {token}
```

## 数据库设计

### 简历表（user_resume）
```sql
CREATE TABLE `user_resume` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `resume_name` varchar(200) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `stored_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `file_type` varchar(10) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `parse_status` tinyint(1) NOT NULL DEFAULT '0',
  `parsed_content` longtext,
  `parse_error` text,
  `parsed_at` datetime DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
);
```

## 配置说明

### application.yml配置
```yaml
# 文件上传配置
file:
  upload:
    resume:
      path: ./uploads/resumes  # 存储路径
      max-size: 10485760      # 最大文件大小（10MB）
      allowed-types: pdf,doc,docx,txt
      cleanup-days: 365       # 文件保留天数

# Gemini AI配置
gemini:
  enabled: false            # 是否启用AI解析
  api:
    key: ""                # API密钥
    url: "https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent"
    timeout: 30000         # 超时时间
    retry-times: 3         # 重试次数
```

## 部署指南

### 1. 环境准备
- Java 8+
- MySQL 5.7+
- Redis（可选，用于缓存）

### 2. 数据库初始化
```bash
# 执行建表SQL
mysql -u root -p jobplusv8 < src/main/resources/sql/resume_table.sql
```

### 3. 配置文件
```bash
# 修改application.yml中的数据库连接
# 配置文件存储路径
# 配置Gemini API Key（如需AI解析功能）
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 访问测试
- API文档：http://localhost:80/swagger-ui.html
- 上传页面：http://localhost:80/resume-upload.html

## 使用说明

### 1. 简历上传
1. 访问上传页面或调用API
2. 选择支持格式的简历文件
3. 输入简历名称（建议：姓名+岗位）
4. 选择是否设为默认简历
5. 提交上传

### 2. 查看管理
1. 通过API获取简历列表
2. 查看简历详情和解析结果
3. 修改简历名称
4. 设置默认简历
5. 下载或删除简历

### 3. AI解析
1. 上传后自动触发解析
2. 查看解析状态和结果
3. 支持重新解析功能
4. 解析结果JSON格式存储

## 注意事项

1. **文件安全**：上传的文件会重命名存储，防止文件名冲突
2. **权限控制**：所有操作都需要用户认证，只能操作自己的简历
3. **存储管理**：建议定期清理过期文件，配置文件保留策略
4. **AI配额**：Gemini API有使用限制，注意配额管理
5. **错误处理**：完善的异常处理，用户友好的错误提示

## 扩展功能

### 可扩展的功能点
1. **简历模板**：提供标准简历模板
2. **批量操作**：支持批量上传、删除
3. **版本管理**：同一简历的多个版本管理
4. **分享功能**：生成简历分享链接
5. **统计分析**：简历上传、解析统计
6. **格式转换**：不同格式间的转换
7. **在线预览**：支持在线预览简历内容
8. **搜索功能**：基于解析内容的搜索

## 故障排除

### 常见问题
1. **上传失败**：检查文件大小和格式
2. **解析失败**：检查Gemini API配置和网络
3. **下载失败**：检查文件是否存在
4. **权限错误**：检查JWT token是否有效

### 日志查看
```bash
# 查看应用日志
tail -f logs/jobplusv8.log

# 查看上传相关日志
grep "上传简历" logs/jobplusv8.log

# 查看解析相关日志
grep "Gemini解析" logs/jobplusv8.log
```
