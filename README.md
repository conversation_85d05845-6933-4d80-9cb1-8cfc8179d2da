# JobPlusV8 - 智能面试系统

## 🎯 项目概述

JobPlusV8 是一个基于 Spring Boot 的智能面试系统，集成了实时语音识别、用户积分管理、简历管理等功能。系统采用现代化的微服务架构，支持实时音频流处理、用户权限控制和完整的业务流程管理。

### 🌟 核心特性
- **实时语音识别** - 基于腾讯云语音识别服务，支持实时音频转文字
- **用户积分系统** - 完整的积分管理、充值、消费记录功能
- **简历管理** - 支持简历上传、解析、管理和搜索
- **用户中心** - 账号管理、密码修改、积分查看、充值记录
- **权限控制** - 基于JWT的认证授权和积分权限控制

## 🛠️ 技术栈

- **框架**: Spring Boot 2.7.6
- **Java版本**: JDK 1.8
- **数据库**: MySQL + MyBatis Plus
- **安全认证**: Spring Security + JWT
- **语音识别**: 腾讯云语音识别 SDK 1.0.54
- **实时通信**: WebSocket
- **邮件服务**: Spring Boot Mail
- **API文档**: Swagger 3.0 (OpenAPI 3.0)
- **工具库**: Lombok, Apache Commons Lang3

## 📁 项目结构

```
src/main/java/cloud/ipanda/jobplusv8/
├── Jobplusv8Application.java          # 主启动类
├── config/                            # 配置类
│   ├── TencentSpeechConfig.java       # 腾讯云语音配置
│   ├── WebSocketConfiguration.java    # WebSocket配置
│   ├── SwaggerConfig.java             # Swagger API文档配置
│   ├── SecurityConfig.java            # Spring Security配置
│   └── WebConfig.java                 # Web配置（拦截器注册）
├── controller/                        # 控制器层
│   ├── InterviewController.java       # 面试管理API
│   ├── UserCenterController.java      # 用户中心API
│   ├── AdminRechargeCodeController.java # 管理员充值码API
│   ├── ResumeController.java          # 简历管理API
│   ├── AuthController.java            # 认证授权API
│   ├── SpeechDurationController.java  # 语音积分管理API
│   └── EmailController.java           # 邮件服务API
├── entity/                            # 实体类
│   ├── User.java                      # 用户实体
│   ├── Resume.java                    # 简历实体
│   ├── PointsRecharge.java            # 积分充值记录
│   ├── PointsConsumption.java         # 积分消费记录
│   └── RechargeCode.java              # 充值码实体
├── service/                           # 服务层
│   ├── UserCenterService.java         # 用户中心服务
│   ├── PointsService.java             # 积分服务
│   ├── RechargeCodeService.java       # 充值码服务
│   └── ResumeService.java             # 简历服务
├── interceptor/                       # 拦截器
│   └── PointsInterceptor.java         # 积分权限拦截器
├── aspect/                            # 切面
│   └── PointsConsumptionAspect.java   # 积分消费切面
├── util/                              # 工具类
│   └── JwtUtil.java                   # JWT工具类
└── websocket/                         # WebSocket处理
    └── TencentInterviewWebsocket.java # 面试WebSocket端点
```

## 🚀 核心功能

### 1. 用户积分系统
- **积分管理**: 用户积分余额管理，支持充值和消费
- **充值功能**:
  - 充值码充值（加密安全）
  - 管理员直充（任意积分）
- **消费规则**:
  - 面试功能：10积分/次
  - 语音识别：1积分/秒
  - AI答复：3积分/次
- **记录追踪**: 完整的充值和消费记录，支持分页查询

### 2. 用户中心
- **账号管理**: 查看个人信息、修改密码
- **积分查看**: 当前积分余额、可用时长显示
- **历史记录**: 充值记录、消费记录分页查询
- **安全验证**: 密码修改需验证当前密码

### 3. 实时语音识别
- **基于积分**: 1秒消耗1积分，精确计费
- **实时处理**: WebSocket实时音频流传输
- **多格式支持**: 支持多种音频格式
- **权限控制**: 积分不足时自动拦截

### 4. 简历管理
- **简历上传**: 支持PDF、Word等格式
- **内容解析**: 自动提取简历关键信息
- **搜索功能**: 支持关键词搜索和筛选
- **权限控制**: 用户只能管理自己的简历

### 5. 认证授权
- **JWT认证**: 基于Token的无状态认证
- **权限控制**: 细粒度的API权限管理
- **安全拦截**: 自动检查用户权限和积分

## 🚀 快速开始

### 1. 环境要求
- JDK 1.8+
- MySQL 5.7+
- Maven 3.6+

### 2. 数据库初始化
```bash
# 按顺序执行SQL文件
mysql -u root -p jobplusv8 < src/main/resources/sql/1_user_points_system.sql
mysql -u root -p jobplusv8 < src/main/resources/sql/2_init_test_data.sql
mysql -u root -p jobplusv8 < src/main/resources/sql/3_verify_installation.sql
```

### 3. 配置文件
```yaml
# application.yml
spring:
  datasource:
    url: *************************************
    username: your_username
    password: your_password

# 腾讯云配置
tencent:
  speech:
    secretId: your_secret_id
    secretKey: your_secret_key
    region: ap-beijing
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 访问系统
- **API文档**: http://localhost:80/swagger-ui.html
- **用户中心**: http://localhost:80/user-center.html

## 📚 更新日志

### 2025年8月4日 - 用户积分系统
- ✅ 完整的用户积分管理系统
- ✅ 充值码充值和管理员直充功能
- ✅ 积分消费记录和权限控制
- ✅ 用户中心界面和API
- ✅ 语音识别基于积分计费（1秒=1积分）

### 2025年7月22日 - 邮件服务优化
- 集成Spring Boot Mail发送邮件功能
- 支持HTML格式邮件模板和验证码管理
- 提供面试邀请邮件发送功能

### 2025年7月21日 - Swagger API文档集成
- 集成Swagger 3.0 (OpenAPI 3.0) 文档生成
- 提供完整的REST API接口文档和在线测试功能
- 访问地址：http://localhost:80/swagger-ui.html

## 📖 API文档

详细的API文档请查看 `api/` 目录：
- [用户中心API](api/user-center-api.md) - 用户信息、密码修改、积分查看
- [积分管理API](api/points-management-api.md) - 积分充值、消费记录
- [管理员API](api/admin-api.md) - 充值码管理、用户管理

## 📝 文档规则

### 文档目录结构
```
docs/
├── api/          # API接口文档
├── guides/       # 开发指南和使用说明
├── fixes/        # 技术问题修复记录
└── features/     # 功能特性说明文档
```

### 文档维护规范
- **新增API文档**: 添加到 `docs/api/` 目录
- **开发指南**: 添加到 `docs/guides/` 目录
- **修复记录**: 添加到 `docs/fixes/` 目录
- **功能说明**: 添加到 `docs/features/` 目录
- **文档格式**: 统一使用Markdown格式
- **命名规范**: 使用英文命名，描述性文件名
- **更新维护**: 及时更新相关链接和内容

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 Apache License 2.0 许可证。

---

**最后更新**: 2025年8月4日 (用户积分系统)
**版本**: v1.0.0
